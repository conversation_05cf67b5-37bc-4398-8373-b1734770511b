<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Chat JS - Mensagens em Tempo Real</title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="Aplicativo de chat em tempo real com JavaScript">
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Chat JS">
    <meta name="msapplication-TileColor" content="#667eea">
    <meta name="msapplication-TileImage" content="/icons/icon-144x144.png">

    <!-- PWA Icons -->
    <link rel="icon" type="image/svg+xml" href="/icons/icon.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-16x16.png">
    <link rel="apple-touch-icon" href="/icons/icon-180x180.png">
    <link rel="mask-icon" href="/icons/icon.svg" color="#667eea">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <link rel="stylesheet" href="style.css">
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <div id="app">
        <!-- Tela de Login -->
        <div id="login-screen" class="screen active">
            <div class="auth-container">
                <h1>🚀 Chat Simples</h1>
                <p>Entre no chat em tempo real!</p>

                <div class="auth-tabs">
                    <button class="auth-tab-btn active" data-tab="login">Entrar</button>
                    <button class="auth-tab-btn" data-tab="register">Criar Conta</button>
                </div>

                <!-- Formulário de Login -->
                <form id="login-form" class="auth-form active">
                    <div class="input-group">
                        <label for="login-email">Email:</label>
                        <input type="email" id="login-email" placeholder="<EMAIL>" required>
                    </div>

                    <div class="input-group">
                        <label for="login-password">Senha:</label>
                        <input type="password" id="login-password" placeholder="Sua senha" required>
                    </div>

                    <button type="submit" class="btn-primary">Entrar</button>

                    <div class="auth-footer">
                        <p>Não tem conta? <a href="#" id="show-register">Criar conta</a></p>
                    </div>
                </form>

                <!-- Formulário de Registro -->
                <form id="register-form" class="auth-form">
                    <div class="input-group">
                        <label for="register-name">Nome Completo:</label>
                        <input type="text" id="register-name" placeholder="Seu nome completo" required>
                        <span class="input-error" id="name-error"></span>
                    </div>

                    <div class="input-group">
                        <label for="register-email">Email:</label>
                        <input type="email" id="register-email" placeholder="<EMAIL>" required>
                        <span class="input-error" id="email-error"></span>
                    </div>

                    <div class="input-group">
                        <label for="register-password">Senha:</label>
                        <input type="password" id="register-password" placeholder="Mínimo 6 caracteres" required>
                        <span class="input-error" id="password-error"></span>
                    </div>

                    <div class="input-group">
                        <label for="register-confirm-password">Confirmar Senha:</label>
                        <input type="password" id="register-confirm-password" placeholder="Digite a senha novamente" required>
                        <span class="input-error" id="confirm-password-error"></span>
                    </div>

                    <div class="input-group">
                        <label for="avatar">Escolha um Avatar:</label>
                        <div class="avatar-selector">
                            <button type="button" class="avatar-btn active" data-avatar="😊">😊</button>
                            <button type="button" class="avatar-btn" data-avatar="😎">😎</button>
                            <button type="button" class="avatar-btn" data-avatar="🤖">🤖</button>
                            <button type="button" class="avatar-btn" data-avatar="🦄">🦄</button>
                            <button type="button" class="avatar-btn" data-avatar="🐱">🐱</button>
                            <button type="button" class="avatar-btn" data-avatar="🚀">🚀</button>
                            <button type="button" class="avatar-btn" data-avatar="🌟">🌟</button>
                            <button type="button" class="avatar-btn" data-avatar="🎯">🎯</button>
                        </div>
                    </div>

                    <button type="submit" class="btn-primary">Criar Conta</button>

                    <div class="auth-footer">
                        <p>Já tem conta? <a href="#" id="show-login">Fazer login</a></p>
                    </div>
                </form>

                <!-- Loading -->
                <div id="auth-loading" class="auth-loading">
                    <div class="spinner"></div>
                    <p>Processando...</p>
                </div>
            </div>
        </div>

        <!-- Tela Principal do Chat -->
        <div id="chat-screen" class="screen">
            <div class="chat-container">
                <!-- Header -->
                <header class="chat-header">
                    <div class="user-info">
                        <button id="mobile-menu-btn" class="mobile-menu-btn">☰</button>
                        <span id="user-avatar" class="user-avatar">😊</span>
                        <div>
                            <div id="user-name" class="user-name">Usuário</div>
                            <div id="user-id" class="user-id">#1</div>
                        </div>
                    </div>

                    <div class="header-actions">
                        <div class="connection-status">
                            <span id="connection-indicator" class="status-indicator offline">●</span>
                            <span id="connection-text">Desconectado</span>
                        </div>
                        <button id="logout-btn" class="btn-logout">Sair</button>
                    </div>
                </header>

                <!-- Overlay para mobile -->
                <div id="sidebar-overlay" class="sidebar-overlay"></div>

                <!-- Sidebar -->
                <aside class="sidebar">
                    <div class="sidebar-tabs">
                        <button class="tab-btn active" data-tab="friends">👥 Contatos</button>
                        <button class="tab-btn" data-tab="requests">💌 Convites</button>
                    </div>

                    <!-- Aba Amigos -->
                    <div id="friends-tab" class="tab-content active">
                        <div class="tab-header">
                            <h3>Conversas</h3>
                            <button id="add-friend-btn" class="btn-small">+ Adicionar</button>
                        </div>
                        <div id="conversations-list" class="conversations-list">
                            <div class="empty-state">
                                <p>Nenhuma conversa ainda</p>
                                <p>Adicione amigos para começar!</p>
                            </div>
                        </div>
                    </div>

                    <!-- Aba Convites -->
                    <div id="requests-tab" class="tab-content">
                        <div class="tab-header">
                            <h3>Pedidos de Amizade</h3>
                        </div>
                        <div id="friend-requests-list" class="friend-requests-list">
                            <div class="empty-state">
                                <p>Nenhum convite pendente</p>
                            </div>
                        </div>
                    </div>


                </aside>

                <!-- Área de Chat -->
                <main class="chat-main">
                    <div id="welcome-message" class="welcome-message">
                        <h2>Bem-vindo ao Chat! 🎉</h2>
                        <p>Selecione uma conversa ou adicione amigos para começar</p>
                    </div>

                    <div id="chat-area" class="chat-area hidden">
                        <div class="chat-header-conversation">
                            <span id="conversation-avatar" class="conversation-avatar">👤</span>
                            <div>
                                <div id="conversation-name" class="conversation-name">Amigo</div>
                                <div id="conversation-status" class="conversation-status">online</div>
                            </div>
                        </div>

                        <div id="messages-container" class="messages-container">
                            <!-- Mensagens aparecerão aqui -->
                        </div>

                        <div class="message-input-container">
                            <form id="message-form">
                                <input type="text" id="message-input" placeholder="Digite sua mensagem..." required>
                                <button type="submit" class="btn-send">📤</button>
                            </form>
                        </div>
                    </div>
                </main>
            </div>
        </div>

        <!-- Modal Adicionar Amigo -->
        <div id="add-friend-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Adicionar Amigo</h3>
                    <button id="close-modal" class="btn-close">×</button>
                </div>
                <div class="modal-body">
                    <form id="add-friend-form">
                        <div class="input-group">
                            <label for="friend-search">Buscar por nome ou ID:</label>
                            <input type="text" id="friend-search" placeholder="Digite o nome ou #ID do usuário">
                        </div>
                        <div id="search-results" class="search-results">
                            <!-- Resultados da busca aparecerão aqui -->
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
