
import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { User, AlertCircle } from 'lucide-react';
import { toast } from '@/components/ui/sonner';

interface AuthScreenProps {
  onLogin: (userData: {
    id: string;
    name: string;
    username: string;
    password: string;
    displayUsername: string;
    sequentialId: number;
    avatar: string;
    status: 'online';
    createdAt: string;
  }) => void;
}

const AuthScreen = ({ onLogin }: AuthScreenProps) => {
  const [loginData, setLoginData] = useState({ username: '', password: '' });
  const [registerData, setRegisterData] = useState({
    name: '',
    username: '',
    password: '',
    confirmPassword: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [registeredUsersCount, setRegisteredUsersCount] = useState(0);

  // Migration function to move old data to shared database
  useEffect(() => {
    migrateToSharedDatabase();
  }, []);

  const updateRegisteredUsersCount = useCallback(() => {
    const users = getAllRegisteredUsers();
    setRegisteredUsersCount(users.length);
  }, []);

  // Update count on component mount (don't clear data automatically)
  useEffect(() => {
    updateRegisteredUsersCount();
  }, [updateRegisteredUsersCount]);



  // Shared database key for cross-browser compatibility
  const SHARED_USERS_KEY = 'shared_registered_users_db';
  const SHARED_COUNTER_KEY = 'shared_user_counter';

  const generateNextUserId = () => {
    const lastUserId = localStorage.getItem(SHARED_COUNTER_KEY);
    const nextId = lastUserId ? parseInt(lastUserId) + 1 : 1;
    localStorage.setItem(SHARED_COUNTER_KEY, nextId.toString());
    return nextId;
  };

  const getAllRegisteredUsers = () => {
    // First, try to get from shared database
    const sharedUsersData = localStorage.getItem(SHARED_USERS_KEY);
    if (sharedUsersData) {
      try {
        return JSON.parse(sharedUsersData);
      } catch (error) {
        console.error('Error parsing shared users data:', error);
      }
    }

    // Fallback: migrate old individual user records to shared database
    const users = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('registered_user_')) {
        try {
          const userData = JSON.parse(localStorage.getItem(key) || '');
          users.push(userData);
        } catch (error) {
          console.error('Error parsing user data:', error);
        }
      }
    }

    // Save to shared database and clean up old records
    if (users.length > 0) {
      localStorage.setItem(SHARED_USERS_KEY, JSON.stringify(users));
      // Clean up old individual records
      users.forEach(user => {
        localStorage.removeItem(`registered_user_${user.username}`);
      });
    }

    return users;
  };

  const migrateToSharedDatabase = () => {
    // Check if migration is needed
    const sharedData = localStorage.getItem(SHARED_USERS_KEY);
    if (sharedData) return; // Already migrated

    // Migrate old individual user records
    const users = [];
    const keysToRemove = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('registered_user_')) {
        try {
          const userData = JSON.parse(localStorage.getItem(key) || '');
          users.push(userData);
          keysToRemove.push(key);
        } catch (error) {
          console.error('Error parsing user data during migration:', error);
        }
      }
    }

    if (users.length > 0) {
      localStorage.setItem(SHARED_USERS_KEY, JSON.stringify(users));
      // Clean up old records
      keysToRemove.forEach(key => localStorage.removeItem(key));
      console.log(`Migrated ${users.length} users to shared database`);
    }
  };

  const saveUserToSharedDatabase = (userData: any) => {
    const existingUsers = getAllRegisteredUsers();
    const updatedUsers = [...existingUsers, userData];
    localStorage.setItem(SHARED_USERS_KEY, JSON.stringify(updatedUsers));
  };

  const validateLogin = (username: string, password: string) => {
    const allUsers = getAllRegisteredUsers();
    const user = allUsers.find(u => u.username === username);

    if (!user) {
      return { success: false, message: 'Usuário não encontrado. Você precisa se cadastrar primeiro.' };
    }

    if (user.password === password) {
      return { success: true, user: user };
    } else {
      return { success: false, message: 'Senha incorreta.' };
    }
  };

  const handleLogin = async () => {
    if (!loginData.username || !loginData.password) {
      toast.error('Por favor, preencha todos os campos.');
      return;
    }

    setIsLoading(true);

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const validation = validateLogin(loginData.username, loginData.password);

    if (validation.success && validation.user) {
      toast.success('Login realizado com sucesso!');
      onLogin(validation.user);
    } else {
      toast.error(validation.message);
    }

    setIsLoading(false);
  };

  const handleRegister = async () => {
    if (!registerData.name || !registerData.username || !registerData.password || !registerData.confirmPassword) {
      toast.error('Por favor, preencha todos os campos.');
      return;
    }

    if (registerData.password !== registerData.confirmPassword) {
      toast.error('As senhas não coincidem.');
      return;
    }

    if (registerData.password.length < 4) {
      toast.error('A senha deve ter pelo menos 4 caracteres.');
      return;
    }

    setIsLoading(true);

    // Check if username already exists
    const allUsers = getAllRegisteredUsers();
    const existingUser = allUsers.find((u: any) => u.username === registerData.username);
    if (existingUser) {
      toast.error('Nome de usuário já existe. Escolha outro.');
      setIsLoading(false);
      return;
    }

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Create new user with sequential ID
    const userId = generateNextUserId();
    const userData = {
      id: `user_${Date.now()}`,
      name: registerData.name,
      username: registerData.username, // Login username
      password: registerData.password, // In a real app, this should be hashed
      displayUsername: `#${userId}`, // Display tag (#1, #2, #3...)
      sequentialId: userId, // Store the sequential number
      avatar: '😊',
      status: 'online' as const,
      createdAt: new Date().toISOString(),
    };

    // Save user data to shared database
    saveUserToSharedDatabase(userData);

    toast.success(`Conta criada com sucesso! Seu ID é #${userId}`);

    // Update registered users count
    updateRegisteredUsersCount();

    // Auto login after registration
    onLogin(userData);
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <User className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            ChatApp
          </h1>
          <p className="text-gray-600 mt-2">Conecte-se com seus amigos</p>
          <div className="text-center">
            <p className="text-sm text-gray-500 mt-2">
              👥 {registeredUsersCount} usuário{registeredUsersCount !== 1 ? 's' : ''} cadastrado{registeredUsersCount !== 1 ? 's' : ''}
            </p>

          </div>
        </div>

        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-center">Bem-vindo!</CardTitle>
            <CardDescription className="text-center">
              Entre na sua conta ou crie uma nova
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="login" className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="login">Entrar</TabsTrigger>
                <TabsTrigger value="register">Cadastrar</TabsTrigger>
              </TabsList>
              
              <TabsContent value="login" className="space-y-4">
                <div className="space-y-2">
                  <Input
                    placeholder="Nome de usuário"
                    value={loginData.username}
                    onChange={(e) => setLoginData({ ...loginData, username: e.target.value })}
                    onKeyDown={(e) => e.key === 'Enter' && handleLogin()}
                    disabled={isLoading}
                    className="h-12"
                  />
                  <Input
                    type="password"
                    placeholder="Senha"
                    value={loginData.password}
                    onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
                    onKeyDown={(e) => e.key === 'Enter' && handleLogin()}
                    disabled={isLoading}
                    className="h-12"
                  />
                </div>
                <Button
                  onClick={handleLogin}
                  disabled={isLoading}
                  className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:opacity-50"
                >
                  {isLoading ? 'Entrando...' : 'Entrar'}
                </Button>
              </TabsContent>
              
              <TabsContent value="register" className="space-y-4">
                <div className="space-y-2">
                  <Input
                    placeholder="Nome completo"
                    value={registerData.name}
                    onChange={(e) => setRegisterData({ ...registerData, name: e.target.value })}
                    disabled={isLoading}
                    className="h-12"
                  />
                  <Input
                    placeholder="Nome de usuário"
                    value={registerData.username}
                    onChange={(e) => setRegisterData({ ...registerData, username: e.target.value })}
                    disabled={isLoading}
                    className="h-12"
                  />
                  <Input
                    type="password"
                    placeholder="Senha"
                    value={registerData.password}
                    onChange={(e) => setRegisterData({ ...registerData, password: e.target.value })}
                    disabled={isLoading}
                    className="h-12"
                  />
                  <Input
                    type="password"
                    placeholder="Confirmar senha"
                    value={registerData.confirmPassword}
                    onChange={(e) => setRegisterData({ ...registerData, confirmPassword: e.target.value })}
                    onKeyDown={(e) => e.key === 'Enter' && handleRegister()}
                    disabled={isLoading}
                    className="h-12"
                  />
                </div>
                <Button
                  onClick={handleRegister}
                  disabled={isLoading}
                  className="w-full h-12 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50"
                >
                  {isLoading ? 'Criando...' : 'Criar Conta'}
                </Button>
                <div className="text-xs text-gray-500 text-center space-y-1">
                  <p>Ao criar sua conta, você receberá um ID único sequencial (#1, #2, #3...) para que amigos possam te encontrar!</p>
                  <p className="text-red-500 font-medium">⚠️ Agora é necessário ter uma conta cadastrada para fazer login!</p>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AuthScreen;

