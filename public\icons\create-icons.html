<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON><PERSON></title>
</head>
<body>
    <h1><PERSON><PERSON><PERSON> PWA</h1>
    <canvas id="canvas"></canvas>
    
    <script>
        // Criar ícone simples
        function createSimpleIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Chat icon
            ctx.fillStyle = 'white';
            ctx.font = `${size * 0.3}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText('💬', size/2, size*0.6);
            
            return canvas;
        }
        
        // Gerar todos os tamanhos
        const sizes = [16, 32, 72, 96, 128, 144, 152, 180, 192, 384, 512];
        
        sizes.forEach(size => {
            const canvas = createSimpleIcon(size);
            canvas.toBlob(blob => {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `icon-${size}x${size}.png`;
                a.click();
                URL.revokeObjectURL(url);
            });
        });
    </script>
</body>
</html>
