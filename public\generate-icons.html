<!DOCTYPE html>
<html>
<head>
    <title>Gerador de Ícones PWA</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        .icon-preview { display: inline-block; margin: 10px; text-align: center; }
        .download-btn { background: #667eea; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🎨 Gerador de Ícones PWA - Chat JS</h1>
    <p>Clique no botão para gerar todos os ícones necessários:</p>
    
    <button onclick="generateAllIcons()" class="download-btn">📱 Gerar Todos os Ícones</button>
    
    <div id="icons-container"></div>
    
    <script>
        const sizes = [16, 32, 72, 96, 128, 144, 152, 180, 192, 384, 512];
        
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // Draw background with rounded corners
            const radius = size * 0.15;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            
            // Chat bubble 1
            const scale = size / 512;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.beginPath();
            ctx.roundRect(120 * scale, 150 * scale, 180 * scale, 100 * scale, 30 * scale);
            ctx.fill();
            
            // Chat bubble 2
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.beginPath();
            ctx.roundRect(182 * scale, 210 * scale, 180 * scale, 100 * scale, 30 * scale);
            ctx.fill();
            
            // Chat dots 1
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(225 * scale, 200 * scale, 8 * scale, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(255 * scale, 200 * scale, 8 * scale, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(285 * scale, 200 * scale, 8 * scale, 0, 2 * Math.PI);
            ctx.fill();
            
            // Chat dots 2
            ctx.fillStyle = '#764ba2';
            ctx.beginPath();
            ctx.arc(242 * scale, 260 * scale, 8 * scale, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(272 * scale, 260 * scale, 8 * scale, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(302 * scale, 260 * scale, 8 * scale, 0, 2 * Math.PI);
            ctx.fill();
            
            // Text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${48 * scale}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText('Chat', size / 2, 380 * scale);
            
            ctx.font = `${32 * scale}px Arial`;
            ctx.globalAlpha = 0.8;
            ctx.fillText('JS', size / 2, 420 * scale);
            ctx.globalAlpha = 1;
            
            return canvas;
        }
        
        function downloadCanvas(canvas, filename) {
            canvas.toBlob(function(blob) {
                const link = document.createElement('a');
                link.download = filename;
                link.href = URL.createObjectURL(blob);
                link.click();
                URL.revokeObjectURL(link.href);
            }, 'image/png');
        }
        
        function generateAllIcons() {
            const container = document.getElementById('icons-container');
            container.innerHTML = '<h2>🎨 Gerando ícones...</h2>';
            
            sizes.forEach((size, index) => {
                setTimeout(() => {
                    const canvas = createIcon(size);
                    const filename = `icon-${size}x${size}.png`;
                    
                    // Create preview
                    const preview = document.createElement('div');
                    preview.className = 'icon-preview';
                    preview.innerHTML = `
                        <div>${size}x${size}</div>
                        <canvas width="${Math.min(size, 64)}" height="${Math.min(size, 64)}" style="border: 1px solid #ddd;"></canvas>
                        <br>
                        <button onclick="downloadSingleIcon(${size})" class="download-btn">Download</button>
                    `;
                    
                    // Draw preview
                    const previewCanvas = preview.querySelector('canvas');
                    const previewCtx = previewCanvas.getContext('2d');
                    const previewSize = Math.min(size, 64);
                    previewCtx.drawImage(canvas, 0, 0, previewSize, previewSize);
                    
                    container.appendChild(preview);
                    
                    // Auto download
                    downloadCanvas(canvas, filename);
                    
                    if (index === sizes.length - 1) {
                        container.innerHTML += '<h3>✅ Todos os ícones foram gerados!</h3><p>📁 Mova os arquivos baixados para a pasta <code>public/icons/</code></p>';
                    }
                }, index * 500);
            });
        }
        
        function downloadSingleIcon(size) {
            const canvas = createIcon(size);
            downloadCanvas(canvas, `icon-${size}x${size}.png`);
        }
        
        // Add roundRect polyfill for older browsers
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
