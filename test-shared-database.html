<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Base de Dados Compartilhada</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .user-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .user-item {
            padding: 8px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
        }
        .user-item:last-child {
            border-bottom: none;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🧪 Teste de Base de Dados Compartilhada</h1>
    
    <div class="container">
        <h2>Controles de Teste</h2>
        <button onclick="showCurrentData()">📊 Mostrar Dados Atuais</button>
        <button onclick="createTestUsers()">👥 Criar Usuários de Teste</button>
        <button onclick="clearAllData()">🗑️ Limpar Todos os Dados</button>
        <button onclick="migrateOldData()">🔄 Migrar Dados Antigos</button>
    </div>

    <div class="container">
        <h2>Status</h2>
        <div id="status" class="status info">Pronto para teste</div>
    </div>

    <div class="container">
        <h2>Usuários Registrados</h2>
        <div id="userList" class="user-list">
            <p>Clique em "Mostrar Dados Atuais" para ver os usuários</p>
        </div>
    </div>

    <div class="container">
        <h2>Instruções</h2>
        <ol>
            <li><strong>Teste Cross-Browser:</strong>
                <ul>
                    <li>Abra esta página no Chrome</li>
                    <li>Crie alguns usuários de teste</li>
                    <li>Abra a mesma página no Edge</li>
                    <li>Verifique se os mesmos usuários aparecem</li>
                </ul>
            </li>
            <li><strong>Teste da Aplicação:</strong>
                <ul>
                    <li>Vá para <a href="http://localhost:8081" target="_blank">localhost:8081</a> no Chrome</li>
                    <li>Faça login com um usuário</li>
                    <li>Abra <a href="http://localhost:8081" target="_blank">localhost:8081</a> no Edge</li>
                    <li>Faça login com outro usuário</li>
                    <li>Teste o chat entre os dois navegadores</li>
                </ul>
            </li>
        </ol>
    </div>

    <script>
        const SHARED_USERS_KEY = 'shared_registered_users_db';
        const SHARED_COUNTER_KEY = 'shared_user_counter';

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function showCurrentData() {
            const sharedData = localStorage.getItem(SHARED_USERS_KEY);
            const counter = localStorage.getItem(SHARED_COUNTER_KEY);
            const userListDiv = document.getElementById('userList');

            if (!sharedData) {
                userListDiv.innerHTML = '<p>Nenhum usuário encontrado na base de dados compartilhada</p>';
                showStatus('Nenhum dado encontrado', 'info');
                return;
            }

            try {
                const users = JSON.parse(sharedData);
                let html = `<p><strong>Total de usuários:</strong> ${users.length}</p>`;
                html += `<p><strong>Próximo ID:</strong> ${counter || 'Não definido'}</p>`;
                
                users.forEach(user => {
                    html += `
                        <div class="user-item">
                            <div>
                                <strong>${user.name}</strong> (${user.displayUsername || user.username})
                                <br><small>Login: ${user.username}</small>
                            </div>
                            <div>
                                <small>ID: ${user.sequentialId || 'N/A'}</small>
                            </div>
                        </div>
                    `;
                });

                userListDiv.innerHTML = html;
                showStatus(`${users.length} usuários carregados com sucesso`, 'success');
            } catch (error) {
                userListDiv.innerHTML = '<p>Erro ao carregar dados</p>';
                showStatus('Erro ao carregar dados: ' + error.message, 'error');
            }
        }

        function createTestUsers() {
            const testUsers = [
                { name: 'Alice Silva', username: 'alice', password: '1234' },
                { name: 'Bob Santos', username: 'bob', password: '1234' },
                { name: 'Carol Lima', username: 'carol', password: '1234' }
            ];

            const existingUsers = JSON.parse(localStorage.getItem(SHARED_USERS_KEY) || '[]');
            let counter = parseInt(localStorage.getItem(SHARED_COUNTER_KEY) || '0');
            let created = 0;

            testUsers.forEach(testUser => {
                // Check if user already exists
                const exists = existingUsers.find(u => u.username === testUser.username);
                if (!exists) {
                    counter++;
                    const userData = {
                        id: `user_${Date.now()}_${Math.random()}`,
                        name: testUser.name,
                        username: testUser.username,
                        password: testUser.password,
                        displayUsername: `#${counter}`,
                        sequentialId: counter,
                        avatar: '😊',
                        status: 'online',
                        createdAt: new Date().toISOString(),
                    };
                    existingUsers.push(userData);
                    created++;
                }
            });

            if (created > 0) {
                localStorage.setItem(SHARED_USERS_KEY, JSON.stringify(existingUsers));
                localStorage.setItem(SHARED_COUNTER_KEY, counter.toString());
                showStatus(`${created} usuários de teste criados com sucesso`, 'success');
                showCurrentData();
            } else {
                showStatus('Todos os usuários de teste já existem', 'info');
            }
        }

        function clearAllData() {
            if (confirm('Tem certeza que deseja limpar todos os dados? Esta ação não pode ser desfeita.')) {
                localStorage.removeItem(SHARED_USERS_KEY);
                localStorage.removeItem(SHARED_COUNTER_KEY);
                
                // Also clear any old individual user records
                const keysToRemove = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('registered_user_')) {
                        keysToRemove.push(key);
                    }
                }
                keysToRemove.forEach(key => localStorage.removeItem(key));
                
                showStatus('Todos os dados foram limpos', 'success');
                showCurrentData();
            }
        }

        function migrateOldData() {
            const sharedData = localStorage.getItem(SHARED_USERS_KEY);
            if (sharedData) {
                showStatus('Dados já estão na base compartilhada', 'info');
                return;
            }

            const users = [];
            const keysToRemove = [];
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('registered_user_')) {
                    try {
                        const userData = JSON.parse(localStorage.getItem(key) || '');
                        users.push(userData);
                        keysToRemove.push(key);
                    } catch (error) {
                        console.error('Error parsing user data during migration:', error);
                    }
                }
            }

            if (users.length > 0) {
                localStorage.setItem(SHARED_USERS_KEY, JSON.stringify(users));
                keysToRemove.forEach(key => localStorage.removeItem(key));
                showStatus(`${users.length} usuários migrados para a base compartilhada`, 'success');
                showCurrentData();
            } else {
                showStatus('Nenhum dado antigo encontrado para migrar', 'info');
            }
        }

        // Auto-load data on page load
        window.onload = function() {
            showCurrentData();
        };
    </script>
</body>
</html>
