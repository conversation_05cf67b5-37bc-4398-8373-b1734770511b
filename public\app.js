// Variáveis globais
let socket = null;
let currentUser = null;
let activeConversation = null;
let onlineUsers = [];
let conversations = [];
let friendRequests = [];

// Elementos DOM
const loginScreen = document.getElementById('login-screen');
const chatScreen = document.getElementById('chat-screen');
const loginForm = document.getElementById('login-form');
const registerForm = document.getElementById('register-form');
const authTabButtons = document.querySelectorAll('.auth-tab-btn');
const authForms = document.querySelectorAll('.auth-form');
const authLoading = document.getElementById('auth-loading');
const showRegisterLink = document.getElementById('show-register');
const showLoginLink = document.getElementById('show-login');
const avatarButtons = document.querySelectorAll('.avatar-btn');
const connectionIndicator = document.getElementById('connection-indicator');
const connectionText = document.getElementById('connection-text');
const logoutBtn = document.getElementById('logout-btn');

// Elementos do chat
const userAvatar = document.getElementById('user-avatar');
const userName = document.getElementById('user-name');
const userId = document.getElementById('user-id');
const tabButtons = document.querySelectorAll('.tab-btn');
const tabContents = document.querySelectorAll('.tab-content');
const addFriendBtn = document.getElementById('add-friend-btn');
const addFriendModal = document.getElementById('add-friend-modal');
const closeModalBtn = document.getElementById('close-modal');
const friendSearchInput = document.getElementById('friend-search');
const searchResults = document.getElementById('search-results');

// Elementos de mensagens
const welcomeMessage = document.getElementById('welcome-message');
const chatArea = document.getElementById('chat-area');
const messagesContainer = document.getElementById('messages-container');
const messageForm = document.getElementById('message-form');
const messageInput = document.getElementById('message-input');

console.log('🚀 Chat App Iniciando...');

// Inicialização
document.addEventListener('DOMContentLoaded', () => {
    setupEventListeners();
    checkExistingUser();
});

// Configurar event listeners
function setupEventListeners() {
    // Formulários de autenticação
    loginForm.addEventListener('submit', handleLogin);
    registerForm.addEventListener('submit', handleRegister);

    // Tabs de autenticação
    authTabButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const tabName = btn.dataset.tab;
            switchAuthTab(tabName);
        });
    });

    // Links para trocar entre login e registro
    showRegisterLink.addEventListener('click', (e) => {
        e.preventDefault();
        switchAuthTab('register');
    });

    showLoginLink.addEventListener('click', (e) => {
        e.preventDefault();
        switchAuthTab('login');
    });

    // Seleção de avatar
    avatarButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            avatarButtons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
        });
    });

    // Validação em tempo real
    setupRealTimeValidation();
    
    // Tabs da sidebar
    tabButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const tabName = btn.dataset.tab;
            switchTab(tabName);
        });
    });
    
    // Modal adicionar amigo
    addFriendBtn.addEventListener('click', () => {
        addFriendModal.classList.add('active');
    });
    
    closeModalBtn.addEventListener('click', () => {
        addFriendModal.classList.remove('active');
    });
    
    // Busca de amigos
    friendSearchInput.addEventListener('input', debounce(searchUsers, 300));
    
    // Envio de mensagens
    messageForm.addEventListener('submit', handleSendMessage);

    // Logout
    logoutBtn.addEventListener('click', handleLogout);

    // Fechar modal clicando fora
    addFriendModal.addEventListener('click', (e) => {
        if (e.target === addFriendModal) {
            addFriendModal.classList.remove('active');
        }
    });
}

// Verificar se já existe usuário logado
function checkExistingUser() {
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        showChatScreen();
        connectWebSocket();
    }
}

// Trocar aba de autenticação
function switchAuthTab(tabName) {
    // Atualizar botões
    authTabButtons.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tabName);
    });

    // Atualizar formulários
    authForms.forEach(form => {
        form.classList.toggle('active', form.id === `${tabName}-form`);
    });

    // Limpar erros
    clearValidationErrors();
}

// Configurar validação em tempo real
function setupRealTimeValidation() {
    const registerName = document.getElementById('register-name');
    const registerEmail = document.getElementById('register-email');
    const registerPassword = document.getElementById('register-password');
    const registerConfirmPassword = document.getElementById('register-confirm-password');

    registerName.addEventListener('blur', () => validateName());
    registerEmail.addEventListener('blur', () => validateEmail());
    registerPassword.addEventListener('blur', () => validatePassword());
    registerConfirmPassword.addEventListener('blur', () => validateConfirmPassword());
}

// Lidar com login
function handleLogin(e) {
    e.preventDefault();

    const email = document.getElementById('login-email').value.trim();
    const password = document.getElementById('login-password').value;

    if (!email || !password) {
        showNotification('Por favor, preencha todos os campos', 'error');
        return;
    }

    showLoading(true);
    loginUser(email, password);
}

// Lidar com registro
function handleRegister(e) {
    e.preventDefault();

    const name = document.getElementById('register-name').value.trim();
    const email = document.getElementById('register-email').value.trim();
    const password = document.getElementById('register-password').value;
    const confirmPassword = document.getElementById('register-confirm-password').value;
    const selectedAvatar = document.querySelector('.avatar-btn.active').dataset.avatar;

    // Validar todos os campos
    const isValid = validateName() && validateEmail() && validatePassword() && validateConfirmPassword();

    if (!isValid) {
        showNotification('Por favor, corrija os erros antes de continuar', 'error');
        return;
    }

    showLoading(true);
    registerUser(name, email, password, selectedAvatar);
}

// Funções de validação
function validateName() {
    const name = document.getElementById('register-name').value.trim();
    const errorElement = document.getElementById('name-error');

    if (!name) {
        errorElement.textContent = 'Nome é obrigatório';
        return false;
    }

    if (name.length < 2) {
        errorElement.textContent = 'Nome deve ter pelo menos 2 caracteres';
        return false;
    }

    errorElement.textContent = '';
    return true;
}

function validateEmail() {
    const email = document.getElementById('register-email').value.trim();
    const errorElement = document.getElementById('email-error');

    if (!email) {
        errorElement.textContent = 'Email é obrigatório';
        return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        errorElement.textContent = 'Email inválido';
        return false;
    }

    errorElement.textContent = '';
    return true;
}

function validatePassword() {
    const password = document.getElementById('register-password').value;
    const errorElement = document.getElementById('password-error');

    if (!password) {
        errorElement.textContent = 'Senha é obrigatória';
        return false;
    }

    if (password.length < 6) {
        errorElement.textContent = 'Senha deve ter pelo menos 6 caracteres';
        return false;
    }

    errorElement.textContent = '';
    return true;
}

function validateConfirmPassword() {
    const password = document.getElementById('register-password').value;
    const confirmPassword = document.getElementById('register-confirm-password').value;
    const errorElement = document.getElementById('confirm-password-error');

    if (!confirmPassword) {
        errorElement.textContent = 'Confirmação de senha é obrigatória';
        return false;
    }

    if (password !== confirmPassword) {
        errorElement.textContent = 'Senhas não coincidem';
        return false;
    }

    errorElement.textContent = '';
    return true;
}

function clearValidationErrors() {
    const errorElements = document.querySelectorAll('.input-error');
    errorElements.forEach(element => {
        element.textContent = '';
    });
}

// Mostrar/esconder loading
function showLoading(show) {
    if (show) {
        authLoading.classList.add('active');
    } else {
        authLoading.classList.remove('active');
    }
}

// Fazer login
async function loginUser(email, password) {
    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, password })
        });

        showLoading(false);

        if (response.ok) {
            const user = await response.json();
            currentUser = user;
            localStorage.setItem('currentUser', JSON.stringify(currentUser));

            console.log('✅ Login realizado:', currentUser);
            showNotification(`Bem-vindo de volta, ${currentUser.name}!`, 'success');
            showChatScreen();
            connectWebSocket();
        } else {
            const error = await response.json();
            showNotification(error.error || 'Erro ao fazer login', 'error');
        }
    } catch (error) {
        showLoading(false);
        console.error('❌ Erro no login:', error);
        showNotification('Erro de conexão. Tente novamente.', 'error');
    }
}

// Registrar usuário
async function registerUser(name, email, password, avatar) {
    try {
        const response = await fetch('/api/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ name, email, password, avatar })
        });

        showLoading(false);

        if (response.ok) {
            const user = await response.json();
            currentUser = user;
            localStorage.setItem('currentUser', JSON.stringify(currentUser));

            console.log('✅ Usuário registrado:', currentUser);
            showNotification(`Conta criada com sucesso! Bem-vindo, ${currentUser.name}!`, 'success');
            showChatScreen();
            connectWebSocket();
        } else {
            const error = await response.json();
            showNotification(error.error || 'Erro ao criar conta', 'error');
        }
    } catch (error) {
        showLoading(false);
        console.error('❌ Erro no registro:', error);
        showNotification('Erro de conexão. Tente novamente.', 'error');
    }
}

// Mostrar tela de chat
function showChatScreen() {
    loginScreen.classList.remove('active');
    chatScreen.classList.add('active');

    // Atualizar informações do usuário
    userAvatar.textContent = currentUser.avatar;
    userName.textContent = currentUser.name;
    userId.textContent = currentUser.username;

    // Carregar dados do usuário
    loadUserData();

    // Iniciar timer para atualizar status online a cada 30 segundos
    startOnlineStatusTimer();
}

// Timer para atualizar status online
let onlineStatusTimer = null;

function startOnlineStatusTimer() {
    // Limpar timer anterior se existir
    if (onlineStatusTimer) {
        clearInterval(onlineStatusTimer);
    }

    // Atualizar a cada 30 segundos
    onlineStatusTimer = setInterval(async () => {
        if (conversations.length > 0) {
            await updateFriendsOnlineStatus();
            updateConversationsList();
        }
    }, 30000); // 30 segundos
}

function stopOnlineStatusTimer() {
    if (onlineStatusTimer) {
        clearInterval(onlineStatusTimer);
        onlineStatusTimer = null;
    }
}
    if (onlineStatusTimer) {
        clearInterval(onlineStatusTimer);
        onlineStatusTimer = null;
    }
}

// Carregar dados do usuário (conversas e pedidos)
async function loadUserData() {
    if (!currentUser) return;

    try {
        // Carregar conversas
        const conversationsResponse = await fetch(`/api/conversations/${currentUser.id}`);
        if (conversationsResponse.ok) {
            const userConversations = await conversationsResponse.json();
            conversations = userConversations;

            // Verificar status online dos amigos
            await updateFriendsOnlineStatus();

            updateConversationsList();
            console.log('📋 Conversas carregadas:', conversations.length);
        }

        // Carregar pedidos de amizade
        const requestsResponse = await fetch(`/api/friend-requests/${currentUser.id}`);
        if (requestsResponse.ok) {
            const userRequests = await requestsResponse.json();
            friendRequests = userRequests;
            updateFriendRequestsList();
            console.log('💌 Pedidos carregados:', friendRequests.length);
        }
    } catch (error) {
        console.error('❌ Erro ao carregar dados:', error);
    }
}

// Verificar status online dos amigos
async function updateFriendsOnlineStatus() {
    if (conversations.length === 0) return;

    try {
        const friendIds = conversations.map(conv => conv.friend.id);

        const response = await fetch('/api/check-online-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ userIds: friendIds })
        });

        if (response.ok) {
            const statusList = await response.json();

            // Atualizar status nas conversas
            statusList.forEach(status => {
                const conversation = conversations.find(conv => conv.friend.id === status.userId);
                if (conversation) {
                    conversation.friend.isOnline = status.isOnline;
                    conversation.friend.lastSeen = status.lastSeen;
                }
            });

            console.log('🟢 Status online atualizado para', statusList.length, 'amigos');
        }
    } catch (error) {
        console.error('❌ Erro ao verificar status online:', error);
    }
}

// Fazer logout
function handleLogout() {
    if (confirm('Tem certeza que deseja sair?')) {
        // Parar timer de status online
        stopOnlineStatusTimer();

        // Desconectar WebSocket
        if (socket) {
            socket.disconnect();
            socket = null;
        }

        // Limpar dados locais
        localStorage.removeItem('currentUser');
        currentUser = null;
        activeConversation = null;
        onlineUsers = [];
        conversations = [];
        friendRequests = [];

        // Limpar interface
        messagesContainer.innerHTML = '';
        welcomeMessage.style.display = 'flex';
        chatArea.classList.add('hidden');

        // Voltar para tela de login
        chatScreen.classList.remove('active');
        loginScreen.classList.add('active');

        // Limpar formulários
        loginForm.reset();
        registerForm.reset();
        switchAuthTab('login');
        clearValidationErrors();

        // Atualizar listas vazias
        updateConversationsList();
        updateFriendRequestsList();

        showNotification('Logout realizado com sucesso!', 'info');
        console.log('👋 Logout realizado');
    }
}

// Conectar WebSocket
function connectWebSocket() {
    socket = io();
    
    socket.on('connect', () => {
        console.log('✅ WebSocket conectado');
        updateConnectionStatus(true);
        
        // Fazer login no WebSocket
        socket.emit('user-login', currentUser);
    });
    
    socket.on('disconnect', () => {
        console.log('❌ WebSocket desconectado');
        updateConnectionStatus(false);
    });
    
    // Eventos de usuários (removido - não precisamos mais da lista online)
    
    // Eventos de pedidos de amizade
    socket.on('friend-request-received', (request) => {
        console.log('💌 Pedido recebido:', request);
        friendRequests.push(request);
        updateFriendRequestsList();
        showNotification(`Novo pedido de amizade de ${request.senderName}!`);
    });
    
    socket.on('friend-request-sent', (data) => {
        console.log('📤 Pedido enviado:', data);
        showNotification('Pedido de amizade enviado!');
    });
    
    socket.on('friend-request-accepted', (data) => {
        console.log('✅ Pedido aceito:', data);
        // Adicionar conversa
        const newConversation = {
            id: data.conversationId,
            friend: data.friendData,
            messages: [],
            lastMessage: null
        };

        // Verificar se a conversa já existe
        const existingIndex = conversations.findIndex(c => c.id === data.conversationId);
        if (existingIndex === -1) {
            conversations.push(newConversation);
            updateConversationsList();
            showNotification(`Agora você pode conversar com ${data.friendData.name}!`, 'success');
        }
    });

    socket.on('conversation-created', (data) => {
        console.log('💬 Conversa criada:', data);
        const newConversation = {
            id: data.conversationId,
            friend: data.friendData,
            messages: [],
            lastMessage: null
        };

        // Verificar se a conversa já existe
        const existingIndex = conversations.findIndex(c => c.id === data.conversationId);
        if (existingIndex === -1) {
            conversations.push(newConversation);
            updateConversationsList();
            showNotification(`Nova conversa com ${data.friendData.name}!`, 'success');
        }
    });
    
    // Eventos de mensagens
    socket.on('message-received', (data) => {
        console.log('💬 Mensagem recebida:', data);
        addMessageToConversation(data.conversationId, data.message);
        
        if (activeConversation === data.conversationId) {
            displayMessage(data.message);
        }
    });
}

// Atualizar status de conexão
function updateConnectionStatus(connected) {
    if (connected) {
        connectionIndicator.className = 'status-indicator online';
        connectionText.textContent = 'Conectado';
    } else {
        connectionIndicator.className = 'status-indicator offline';
        connectionText.textContent = 'Desconectado';
    }
}

// Trocar aba
function switchTab(tabName) {
    // Atualizar botões
    tabButtons.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tabName);
    });

    // Atualizar conteúdo
    tabContents.forEach(content => {
        content.classList.toggle('active', content.id === `${tabName}-tab`);
    });

    // Não limpar a conversa ativa - manter o chat aberto
    // A conversa permanece visível independente da aba selecionada
}

// Buscar usuários
async function searchUsers() {
    const query = friendSearchInput.value.trim();

    if (!query) {
        searchResults.innerHTML = '';
        return;
    }

    try {
        const response = await fetch(`/api/search-users?query=${encodeURIComponent(query)}`);
        const users = await response.json();

        // Filtrar usuário atual
        const filteredUsers = users.filter(user => user.id !== currentUser.id);

        displaySearchResults(filteredUsers);
    } catch (error) {
        console.error('❌ Erro na busca:', error);
        showNotification('Erro ao buscar usuários', 'error');
    }
}

// Exibir resultados da busca
function displaySearchResults(users) {
    if (users.length === 0) {
        searchResults.innerHTML = '<p style="text-align: center; color: #666; padding: 1rem;">Nenhum usuário encontrado</p>';
        return;
    }
    
    searchResults.innerHTML = users.map(user => `
        <div class="user-item">
            <div class="user-info-item">
                <span class="user-avatar-item">${user.avatar}</span>
                <div class="user-details">
                    <div class="user-name-item">${user.name}</div>
                    <div class="user-id-item">${user.username}</div>
                </div>
            </div>
            <button class="btn-invite" onclick="sendFriendRequest('${user.id}')">
                Enviar Convite
            </button>
        </div>
    `).join('');
}

// Enviar pedido de amizade
function sendFriendRequest(receiverId) {
    if (!socket) {
        alert('Não conectado ao servidor');
        return;
    }
    
    socket.emit('send-friend-request', {
        senderId: currentUser.id,
        receiverId: receiverId,
        senderData: currentUser
    });
    
    // Fechar modal
    addFriendModal.classList.remove('active');
    friendSearchInput.value = '';
    searchResults.innerHTML = '';
}

// Funções de usuários online removidas - não são mais necessárias

// Atualizar lista de pedidos de amizade
function updateFriendRequestsList() {
    const requestsList = document.getElementById('friend-requests-list');
    
    if (friendRequests.length === 0) {
        requestsList.innerHTML = '<div class="empty-state"><p>Nenhum convite pendente</p></div>';
        return;
    }
    
    requestsList.innerHTML = friendRequests.map(request => `
        <div class="user-item">
            <div class="user-info-item">
                <span class="user-avatar-item">${request.senderAvatar}</span>
                <div class="user-details">
                    <div class="user-name-item">${request.senderName}</div>
                    <div class="user-id-item">${request.senderUsername}</div>
                </div>
            </div>
            <div style="display: flex; gap: 0.5rem;">
                <button class="btn-invite" onclick="acceptFriendRequest('${request.id}')">
                    Aceitar
                </button>
                <button class="btn-invite" style="background: #f87171;" onclick="rejectFriendRequest('${request.id}')">
                    Recusar
                </button>
            </div>
        </div>
    `).join('');
}

// Aceitar pedido de amizade
function acceptFriendRequest(requestId) {
    if (!socket) return;

    socket.emit('accept-friend-request', {
        requestId: requestId,
        userId: currentUser.id
    });

    // Remover da lista local (será atualizada pelo servidor)
    friendRequests = friendRequests.filter(req => req.id !== requestId);
    updateFriendRequestsList();

    showNotification('Pedido de amizade aceito!', 'success');
}

// Recusar pedido de amizade
function rejectFriendRequest(requestId) {
    friendRequests = friendRequests.filter(req => req.id !== requestId);
    updateFriendRequestsList();
}

// Atualizar lista de conversas
function updateConversationsList() {
    const conversationsList = document.getElementById('conversations-list');

    if (conversations.length === 0) {
        conversationsList.innerHTML = '<div class="empty-state"><p>Nenhuma conversa ainda</p><p>Adicione amigos para começar!</p></div>';
        return;
    }

    conversationsList.innerHTML = conversations.map(conv => {
        const lastMessageText = conv.lastMessage ?
            (conv.lastMessage.content.length > 30 ?
                conv.lastMessage.content.substring(0, 30) + '...' :
                conv.lastMessage.content) :
            'Nenhuma mensagem ainda';

        const isActive = activeConversation === conv.id ? 'active' : '';
        const onlineStatus = conv.friend.isOnline ? 'online' : 'offline';
        const statusIcon = conv.friend.isOnline ? '🟢' : '⚫';

        return `
            <div class="user-item conversation-item ${isActive}" onclick="openConversation('${conv.id}')">
                <div class="user-info-item">
                    <span class="user-avatar-item">${conv.friend.avatar || '👤'}</span>
                    <div class="user-details">
                        <div class="user-name-item">
                            ${conv.friend.name}
                            <span class="status-icon ${onlineStatus}">${statusIcon}</span>
                        </div>
                        <div class="user-id-item">${conv.friend.username}</div>
                        <div class="last-message">${lastMessageText}</div>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// Abrir conversa
function openConversation(conversationId) {
    activeConversation = conversationId;
    const conversation = conversations.find(c => c.id === conversationId);

    if (!conversation) {
        console.error('❌ Conversa não encontrada:', conversationId);
        return;
    }

    // Mostrar área de chat
    welcomeMessage.style.display = 'none';
    chatArea.classList.remove('hidden');

    // Atualizar header da conversa
    document.getElementById('conversation-avatar').textContent = conversation.friend.avatar || '👤';
    document.getElementById('conversation-name').textContent = conversation.friend.name;

    const statusText = conversation.friend.isOnline ? 'online' : 'offline';
    const statusElement = document.getElementById('conversation-status');
    statusElement.textContent = statusText;
    statusElement.className = `conversation-status ${statusText}`;

    // Carregar mensagens
    loadMessages(conversationId);

    console.log('💬 Conversa aberta:', conversation.friend.name);
}

// Carregar mensagens
function loadMessages(conversationId) {
    const conversation = conversations.find(c => c.id === conversationId);
    if (!conversation) return;
    
    messagesContainer.innerHTML = '';
    
    if (conversation.messages.length === 0) {
        messagesContainer.innerHTML = '<div style="text-align: center; color: #666; padding: 2rem;">Nenhuma mensagem ainda. Comece a conversar!</div>';
        return;
    }
    
    conversation.messages.forEach(message => {
        displayMessage(message);
    });
    
    // Scroll para baixo
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Exibir mensagem
function displayMessage(message) {
    const isOwn = message.senderId === currentUser.id;
    const messageTime = new Date(message.timestamp).toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
    });
    
    const messageElement = document.createElement('div');
    messageElement.className = `message ${isOwn ? 'own' : ''}`;
    messageElement.innerHTML = `
        <div class="message-avatar">${isOwn ? currentUser.avatar : '👤'}</div>
        <div class="message-content">
            <div class="message-text">${message.content}</div>
            <div class="message-time">${messageTime}</div>
        </div>
    `;
    
    messagesContainer.appendChild(messageElement);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Lidar com envio de mensagem
function handleSendMessage(e) {
    e.preventDefault();
    
    const content = messageInput.value.trim();
    if (!content || !activeConversation || !socket) return;
    
    const conversation = conversations.find(c => c.id === activeConversation);
    if (!conversation) return;
    
    // Enviar via WebSocket
    socket.emit('send-message', {
        conversationId: activeConversation,
        senderId: currentUser.id,
        content: content,
        receiverId: conversation.friend.id
    });
    
    // Limpar input
    messageInput.value = '';
}

// Adicionar mensagem à conversa
function addMessageToConversation(conversationId, message) {
    const conversation = conversations.find(c => c.id === conversationId);
    if (conversation) {
        conversation.messages.push(message);
        conversation.lastMessage = message;

        // Atualizar lista de conversas para mostrar última mensagem
        updateConversationsList();

        console.log('💬 Mensagem adicionada à conversa:', conversationId);
    } else {
        console.error('❌ Conversa não encontrada para adicionar mensagem:', conversationId);
    }
}

// Mostrar notificação
function showNotification(message, type = 'success') {
    // Definir cores baseadas no tipo
    const colors = {
        success: '#4ade80',
        error: '#f87171',
        warning: '#fbbf24',
        info: '#60a5fa'
    };

    // Criar elemento de notificação
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors[type] || colors.success};
        color: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        animation: slideIn 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    notification.textContent = message;
    
    // Adicionar CSS da animação
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(notification);
    
    // Remover após 3 segundos
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Função debounce
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

console.log('✅ Chat App carregado!');
