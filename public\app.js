// Variáveis globais
let socket = null;
let currentUser = null;
let activeConversation = null;
let onlineUsers = [];
let conversations = [];
let friendRequests = [];

// Elementos DOM
const loginScreen = document.getElementById('login-screen');
const chatScreen = document.getElementById('chat-screen');
const loginForm = document.getElementById('login-form');
const nameInput = document.getElementById('name');
const avatarButtons = document.querySelectorAll('.avatar-btn');
const connectionIndicator = document.getElementById('connection-indicator');
const connectionText = document.getElementById('connection-text');

// Elementos do chat
const userAvatar = document.getElementById('user-avatar');
const userName = document.getElementById('user-name');
const userId = document.getElementById('user-id');
const tabButtons = document.querySelectorAll('.tab-btn');
const tabContents = document.querySelectorAll('.tab-content');
const addFriendBtn = document.getElementById('add-friend-btn');
const addFriendModal = document.getElementById('add-friend-modal');
const closeModalBtn = document.getElementById('close-modal');
const friendSearchInput = document.getElementById('friend-search');
const searchResults = document.getElementById('search-results');

// Elementos de mensagens
const welcomeMessage = document.getElementById('welcome-message');
const chatArea = document.getElementById('chat-area');
const messagesContainer = document.getElementById('messages-container');
const messageForm = document.getElementById('message-form');
const messageInput = document.getElementById('message-input');

console.log('🚀 Chat App Iniciando...');

// Inicialização
document.addEventListener('DOMContentLoaded', () => {
    setupEventListeners();
    checkExistingUser();
});

// Configurar event listeners
function setupEventListeners() {
    // Login
    loginForm.addEventListener('submit', handleLogin);
    
    // Seleção de avatar
    avatarButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            avatarButtons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
        });
    });
    
    // Tabs da sidebar
    tabButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const tabName = btn.dataset.tab;
            switchTab(tabName);
        });
    });
    
    // Modal adicionar amigo
    addFriendBtn.addEventListener('click', () => {
        addFriendModal.classList.add('active');
    });
    
    closeModalBtn.addEventListener('click', () => {
        addFriendModal.classList.remove('active');
    });
    
    // Busca de amigos
    friendSearchInput.addEventListener('input', debounce(searchUsers, 300));
    
    // Envio de mensagens
    messageForm.addEventListener('submit', handleSendMessage);
    
    // Fechar modal clicando fora
    addFriendModal.addEventListener('click', (e) => {
        if (e.target === addFriendModal) {
            addFriendModal.classList.remove('active');
        }
    });
}

// Verificar se já existe usuário logado
function checkExistingUser() {
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        showChatScreen();
        connectWebSocket();
    }
}

// Lidar com login
function handleLogin(e) {
    e.preventDefault();
    
    const name = nameInput.value.trim();
    const selectedAvatar = document.querySelector('.avatar-btn.active').dataset.avatar;
    
    if (!name) {
        alert('Por favor, digite seu nome');
        return;
    }
    
    // Criar usuário
    const userData = {
        id: `user_${Date.now()}`,
        name: name,
        avatar: selectedAvatar,
        createdAt: new Date().toISOString()
    };
    
    // Registrar no servidor
    registerUser(userData);
}

// Registrar usuário no servidor
async function registerUser(userData) {
    try {
        const response = await fetch('/api/users', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        });
        
        if (response.ok) {
            const registeredUser = await response.json();
            currentUser = registeredUser;
            localStorage.setItem('currentUser', JSON.stringify(currentUser));
            
            console.log('✅ Usuário registrado:', currentUser);
            showChatScreen();
            connectWebSocket();
        } else {
            const error = await response.json();
            alert(error.error || 'Erro ao registrar usuário');
        }
    } catch (error) {
        console.error('❌ Erro ao registrar:', error);
        alert('Erro de conexão. Tente novamente.');
    }
}

// Mostrar tela de chat
function showChatScreen() {
    loginScreen.classList.remove('active');
    chatScreen.classList.add('active');
    
    // Atualizar informações do usuário
    userAvatar.textContent = currentUser.avatar;
    userName.textContent = currentUser.name;
    userId.textContent = currentUser.username;
}

// Conectar WebSocket
function connectWebSocket() {
    socket = io();
    
    socket.on('connect', () => {
        console.log('✅ WebSocket conectado');
        updateConnectionStatus(true);
        
        // Fazer login no WebSocket
        socket.emit('user-login', currentUser);
    });
    
    socket.on('disconnect', () => {
        console.log('❌ WebSocket desconectado');
        updateConnectionStatus(false);
    });
    
    // Eventos de usuários
    socket.on('user-online', (userData) => {
        console.log('👤 Usuário online:', userData.name);
        addOnlineUser(userData);
    });
    
    socket.on('user-offline', (data) => {
        console.log('👋 Usuário offline:', data.id);
        removeOnlineUser(data.id);
    });
    
    socket.on('online-users-list', (users) => {
        console.log('📋 Lista de usuários online:', users.length);
        onlineUsers = users;
        updateOnlineUsersList();
    });
    
    // Eventos de pedidos de amizade
    socket.on('friend-request-received', (request) => {
        console.log('💌 Pedido recebido:', request);
        friendRequests.push(request);
        updateFriendRequestsList();
        showNotification(`Novo pedido de amizade de ${request.senderName}!`);
    });
    
    socket.on('friend-request-sent', (data) => {
        console.log('📤 Pedido enviado:', data);
        showNotification('Pedido de amizade enviado!');
    });
    
    socket.on('friend-request-accepted', (data) => {
        console.log('✅ Pedido aceito:', data);
        // Adicionar conversa
        conversations.push({
            id: data.conversationId,
            friend: data.friendData,
            messages: []
        });
        updateConversationsList();
        showNotification('Pedido de amizade aceito!');
    });
    
    socket.on('conversation-created', (data) => {
        console.log('💬 Conversa criada:', data);
        conversations.push({
            id: data.conversationId,
            friend: data.friendData,
            messages: []
        });
        updateConversationsList();
    });
    
    // Eventos de mensagens
    socket.on('message-received', (data) => {
        console.log('💬 Mensagem recebida:', data);
        addMessageToConversation(data.conversationId, data.message);
        
        if (activeConversation === data.conversationId) {
            displayMessage(data.message);
        }
    });
}

// Atualizar status de conexão
function updateConnectionStatus(connected) {
    if (connected) {
        connectionIndicator.className = 'status-indicator online';
        connectionText.textContent = 'Conectado';
    } else {
        connectionIndicator.className = 'status-indicator offline';
        connectionText.textContent = 'Desconectado';
    }
}

// Trocar aba
function switchTab(tabName) {
    // Atualizar botões
    tabButtons.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tabName);
    });
    
    // Atualizar conteúdo
    tabContents.forEach(content => {
        content.classList.toggle('active', content.id === `${tabName}-tab`);
    });
}

// Buscar usuários
async function searchUsers() {
    const query = friendSearchInput.value.trim();
    
    if (!query) {
        searchResults.innerHTML = '';
        return;
    }
    
    try {
        const response = await fetch('/api/users');
        const data = await response.json();
        
        const filteredUsers = data.users.filter(user => {
            if (user.id === currentUser.id) return false;
            
            const nameMatch = user.name.toLowerCase().includes(query.toLowerCase());
            const idMatch = user.username === query;
            
            return nameMatch || idMatch;
        });
        
        displaySearchResults(filteredUsers);
    } catch (error) {
        console.error('❌ Erro na busca:', error);
    }
}

// Exibir resultados da busca
function displaySearchResults(users) {
    if (users.length === 0) {
        searchResults.innerHTML = '<p style="text-align: center; color: #666; padding: 1rem;">Nenhum usuário encontrado</p>';
        return;
    }
    
    searchResults.innerHTML = users.map(user => `
        <div class="user-item">
            <div class="user-info-item">
                <span class="user-avatar-item">${user.avatar}</span>
                <div class="user-details">
                    <div class="user-name-item">${user.name}</div>
                    <div class="user-id-item">${user.username}</div>
                </div>
            </div>
            <button class="btn-invite" onclick="sendFriendRequest('${user.id}')">
                Enviar Convite
            </button>
        </div>
    `).join('');
}

// Enviar pedido de amizade
function sendFriendRequest(receiverId) {
    if (!socket) {
        alert('Não conectado ao servidor');
        return;
    }
    
    socket.emit('send-friend-request', {
        senderId: currentUser.id,
        receiverId: receiverId,
        senderData: currentUser
    });
    
    // Fechar modal
    addFriendModal.classList.remove('active');
    friendSearchInput.value = '';
    searchResults.innerHTML = '';
}

// Adicionar usuário online
function addOnlineUser(userData) {
    const existingIndex = onlineUsers.findIndex(u => u.id === userData.id);
    if (existingIndex === -1) {
        onlineUsers.push(userData);
        updateOnlineUsersList();
    }
}

// Remover usuário online
function removeOnlineUser(userId) {
    onlineUsers = onlineUsers.filter(u => u.id !== userId);
    updateOnlineUsersList();
}

// Atualizar lista de usuários online
function updateOnlineUsersList() {
    const onlineList = document.getElementById('online-users-list');
    
    if (onlineUsers.length === 0) {
        onlineList.innerHTML = '<div class="empty-state"><p>Nenhum usuário online</p></div>';
        return;
    }
    
    onlineList.innerHTML = onlineUsers.map(user => `
        <div class="user-item">
            <div class="user-info-item">
                <span class="user-avatar-item">${user.avatar}</span>
                <div class="user-details">
                    <div class="user-name-item">${user.name}</div>
                    <div class="user-id-item">${user.username}</div>
                </div>
            </div>
            <button class="btn-invite" onclick="sendFriendRequest('${user.id}')">
                Enviar Convite
            </button>
        </div>
    `).join('');
}

// Atualizar lista de pedidos de amizade
function updateFriendRequestsList() {
    const requestsList = document.getElementById('friend-requests-list');
    
    if (friendRequests.length === 0) {
        requestsList.innerHTML = '<div class="empty-state"><p>Nenhum convite pendente</p></div>';
        return;
    }
    
    requestsList.innerHTML = friendRequests.map(request => `
        <div class="user-item">
            <div class="user-info-item">
                <span class="user-avatar-item">${request.senderAvatar}</span>
                <div class="user-details">
                    <div class="user-name-item">${request.senderName}</div>
                    <div class="user-id-item">${request.senderUsername}</div>
                </div>
            </div>
            <div style="display: flex; gap: 0.5rem;">
                <button class="btn-invite" onclick="acceptFriendRequest('${request.id}')">
                    Aceitar
                </button>
                <button class="btn-invite" style="background: #f87171;" onclick="rejectFriendRequest('${request.id}')">
                    Recusar
                </button>
            </div>
        </div>
    `).join('');
}

// Aceitar pedido de amizade
function acceptFriendRequest(requestId) {
    if (!socket) return;
    
    socket.emit('accept-friend-request', {
        requestId: requestId,
        userId: currentUser.id
    });
    
    // Remover da lista
    friendRequests = friendRequests.filter(req => req.id !== requestId);
    updateFriendRequestsList();
}

// Recusar pedido de amizade
function rejectFriendRequest(requestId) {
    friendRequests = friendRequests.filter(req => req.id !== requestId);
    updateFriendRequestsList();
}

// Atualizar lista de conversas
function updateConversationsList() {
    const conversationsList = document.getElementById('conversations-list');
    
    if (conversations.length === 0) {
        conversationsList.innerHTML = '<div class="empty-state"><p>Nenhuma conversa ainda</p><p>Adicione amigos para começar!</p></div>';
        return;
    }
    
    conversationsList.innerHTML = conversations.map(conv => `
        <div class="user-item" onclick="openConversation('${conv.id}')">
            <div class="user-info-item">
                <span class="user-avatar-item">${conv.friend.avatar || '👤'}</span>
                <div class="user-details">
                    <div class="user-name-item">${conv.friend.name}</div>
                    <div class="user-id-item">${conv.friend.username}</div>
                </div>
            </div>
        </div>
    `).join('');
}

// Abrir conversa
function openConversation(conversationId) {
    activeConversation = conversationId;
    const conversation = conversations.find(c => c.id === conversationId);
    
    if (!conversation) return;
    
    // Mostrar área de chat
    welcomeMessage.style.display = 'none';
    chatArea.classList.remove('hidden');
    
    // Atualizar header da conversa
    document.getElementById('conversation-avatar').textContent = conversation.friend.avatar || '👤';
    document.getElementById('conversation-name').textContent = conversation.friend.name;
    document.getElementById('conversation-status').textContent = 'online';
    
    // Carregar mensagens
    loadMessages(conversationId);
}

// Carregar mensagens
function loadMessages(conversationId) {
    const conversation = conversations.find(c => c.id === conversationId);
    if (!conversation) return;
    
    messagesContainer.innerHTML = '';
    
    if (conversation.messages.length === 0) {
        messagesContainer.innerHTML = '<div style="text-align: center; color: #666; padding: 2rem;">Nenhuma mensagem ainda. Comece a conversar!</div>';
        return;
    }
    
    conversation.messages.forEach(message => {
        displayMessage(message);
    });
    
    // Scroll para baixo
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Exibir mensagem
function displayMessage(message) {
    const isOwn = message.senderId === currentUser.id;
    const messageTime = new Date(message.timestamp).toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
    });
    
    const messageElement = document.createElement('div');
    messageElement.className = `message ${isOwn ? 'own' : ''}`;
    messageElement.innerHTML = `
        <div class="message-avatar">${isOwn ? currentUser.avatar : '👤'}</div>
        <div class="message-content">
            <div class="message-text">${message.content}</div>
            <div class="message-time">${messageTime}</div>
        </div>
    `;
    
    messagesContainer.appendChild(messageElement);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Lidar com envio de mensagem
function handleSendMessage(e) {
    e.preventDefault();
    
    const content = messageInput.value.trim();
    if (!content || !activeConversation || !socket) return;
    
    const conversation = conversations.find(c => c.id === activeConversation);
    if (!conversation) return;
    
    // Enviar via WebSocket
    socket.emit('send-message', {
        conversationId: activeConversation,
        senderId: currentUser.id,
        content: content,
        receiverId: conversation.friend.id
    });
    
    // Limpar input
    messageInput.value = '';
}

// Adicionar mensagem à conversa
function addMessageToConversation(conversationId, message) {
    const conversation = conversations.find(c => c.id === conversationId);
    if (conversation) {
        conversation.messages.push(message);
    }
}

// Mostrar notificação
function showNotification(message) {
    // Criar elemento de notificação
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4ade80;
        color: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        animation: slideIn 0.3s ease;
    `;
    notification.textContent = message;
    
    // Adicionar CSS da animação
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(notification);
    
    // Remover após 3 segundos
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Função debounce
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

console.log('✅ Chat App carregado!');
