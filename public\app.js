// Variáveis globais
let socket = null;
let currentUser = null;
let activeConversation = null;
let onlineUsers = [];
let conversations = [];
let friendRequests = [];
let isMobile = false;
let isTablet = false;

// Variáveis para áudio e emoji
let mediaRecorder = null;
let audioChunks = [];
let isRecording = false;
let currentAudio = null;
let emojiPickerOpen = false;

// Elementos DOM
const loginScreen = document.getElementById('login-screen');
const chatScreen = document.getElementById('chat-screen');
const loginForm = document.getElementById('login-form');
const registerForm = document.getElementById('register-form');
const authTabButtons = document.querySelectorAll('.auth-tab-btn');
const authForms = document.querySelectorAll('.auth-form');
const authLoading = document.getElementById('auth-loading');
const showRegisterLink = document.getElementById('show-register');
const showLoginLink = document.getElementById('show-login');
const avatarButtons = document.querySelectorAll('.avatar-btn');
const connectionIndicator = document.getElementById('connection-indicator');
const connectionText = document.getElementById('connection-text');
const logoutBtn = document.getElementById('logout-btn');
const mobileMenuBtn = document.getElementById('mobile-menu-btn');
const sidebarOverlay = document.getElementById('sidebar-overlay');
const sidebar = document.querySelector('.sidebar');

// Elementos para emoji e áudio
const emojiBtn = document.getElementById('emoji-btn');
const emojiPicker = document.getElementById('emoji-picker');
const emojiGrid = document.getElementById('emoji-grid');
const emojiCategories = document.querySelectorAll('.emoji-category');
const audioBtn = document.getElementById('audio-btn');
const recordingIndicator = document.getElementById('recording-indicator');

// Debug - verificar se elementos foram encontrados
console.log('🔍 Debug elementos:', {
    emojiBtn: !!emojiBtn,
    emojiPicker: !!emojiPicker,
    emojiGrid: !!emojiGrid,
    emojiCategories: emojiCategories.length,
    audioBtn: !!audioBtn,
    recordingIndicator: !!recordingIndicator
});

// Elementos do chat
const userAvatar = document.getElementById('user-avatar');
const userName = document.getElementById('user-name');
const userId = document.getElementById('user-id');
const tabButtons = document.querySelectorAll('.tab-btn');
const tabContents = document.querySelectorAll('.tab-content');
const addFriendBtn = document.getElementById('add-friend-btn');
const addFriendModal = document.getElementById('add-friend-modal');
const closeModalBtn = document.getElementById('close-modal');
const friendSearchInput = document.getElementById('friend-search');
const searchResults = document.getElementById('search-results');

// Elementos de mensagens
const welcomeMessage = document.getElementById('welcome-message');
const chatArea = document.getElementById('chat-area');
const messagesContainer = document.getElementById('messages-container');
const messageForm = document.getElementById('message-form');
const messageInput = document.getElementById('message-input');

console.log('🚀 Chat App Iniciando...');

// Função para mostrar notificações toast
function showToast(message, type = 'info') {
    console.log(`🍞 Toast ${type}: ${message}`);

    // Criar elemento toast
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    // Estilos inline para garantir que funcione
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        max-width: 300px;
        word-wrap: break-word;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        transition: all 0.3s ease;
        transform: translateX(100%);
    `;

    // Cores por tipo
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };

    toast.style.backgroundColor = colors[type] || colors.info;

    // Adicionar ao DOM
    document.body.appendChild(toast);

    // Animar entrada
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    // Remover após 4 segundos
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 4000);
}

// Banco de dados de emojis por categoria
const emojiDatabase = {
    smileys: [
        '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
        '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
        '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
        '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥'
    ],
    people: [
        '👶', '🧒', '👦', '👧', '🧑', '👱', '👨', '🧔', '👩', '🧓', '👴', '👵', '🙍', '🙎', '🙅', '🙆',
        '💁', '🙋', '🧏', '🙇', '🤦', '🤷', '👮', '🕵️', '💂', '👷', '🤴', '👸', '👳', '👲', '🧕', '🤵',
        '👰', '🤰', '🤱', '👼', '🎅', '🤶', '🦸', '🦹', '🧙', '🧚', '🧛', '🧜', '🧝', '🧞', '🧟', '💆',
        '💇', '🚶', '🏃', '💃', '🕺', '🕴️', '👯', '🧖', '🧗', '🤺', '🏇', '⛷️', '🏂', '🏌️', '🏄', '🚣'
    ],
    animals: [
        '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯', '🦁', '🐮', '🐷', '🐽', '🐸', '🐵',
        '🙈', '🙉', '🙊', '🐒', '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇', '🐺', '🐗',
        '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜', '🦟', '🦗', '🕷️', '🕸️', '🦂', '🐢', '🐍', '🦎',
        '🦖', '🦕', '🐙', '🦑', '🦐', '🦞', '🦀', '🐡', '🐠', '🐟', '🐬', '🐳', '🐋', '🦈', '🐊', '🐅'
    ],
    food: [
        '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝',
        '🍅', '🍆', '🥑', '🥦', '🥬', '🥒', '🌶️', '🫑', '🌽', '🥕', '🫒', '🧄', '🧅', '🥔', '🍠', '🥐',
        '🥯', '🍞', '🥖', '🥨', '🧀', '🥚', '🍳', '🧈', '🥞', '🧇', '🥓', '🥩', '🍗', '🍖', '🦴', '🌭',
        '🍔', '🍟', '🍕', '🥪', '🥙', '🧆', '🌮', '🌯', '🫔', '🥗', '🥘', '🫕', '🍝', '🍜', '🍲', '🍛'
    ],
    travel: [
        '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐', '🛻', '🚚', '🚛', '🚜', '🏍️', '🛵',
        '🚲', '🛴', '🛹', '🛼', '🚁', '🛸', '✈️', '🛩️', '🛫', '🛬', '🪂', '💺', '🚀', '🛰️', '🚊', '🚝',
        '🚄', '🚅', '🚈', '🚂', '🚆', '🚇', '🚉', '🚞', '🚋', '🚃', '🚟', '🚠', '🚡', '⛵', '🛶', '🚤',
        '🛳️', '⛴️', '🚢', '⚓', '⛽', '🚧', '🚨', '🚥', '🚦', '🛑', '🚏', '🗺️', '🗿', '🗽', '🗼', '🏰'
    ],
    activities: [
        '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱', '🪀', '🏓', '🏸', '🏒', '🏑', '🥍',
        '🏏', '🪃', '🥅', '⛳', '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️', '🥌', '🎿',
        '⛷️', '🏂', '🪂', '🏋️', '🤼', '🤸', '⛹️', '🤺', '🤾', '🏌️', '🏇', '🧘', '🏄', '🏊', '🤽', '🚣',
        '🧗', '🚵', '🚴', '🏆', '🥇', '🥈', '🥉', '🏅', '🎖️', '🏵️', '🎗️', '🎫', '🎟️', '🎪', '🤹', '🎭'
    ],
    objects: [
        '💡', '🔦', '🕯️', '🪔', '🧯', '🛢️', '💸', '💵', '💴', '💶', '💷', '🪙', '💰', '💳', '💎', '⚖️',
        '🪜', '🧰', '🔧', '🔨', '⚒️', '🛠️', '⛏️', '🪓', '🪚', '🔩', '⚙️', '🪤', '🧲', '🔫', '💣', '🧨',
        '🪓', '🔪', '🗡️', '⚔️', '🛡️', '🚬', '⚰️', '🪦', '⚱️', '🏺', '🔮', '📿', '🧿', '💈', '⚗️', '🔭',
        '🔬', '🕳️', '🩹', '🩺', '💊', '💉', '🩸', '🧬', '🦠', '🧫', '🧪', '🌡️', '🧹', '🪣', '🧽', '🧴'
    ],
    symbols: [
        '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖',
        '💘', '💝', '💟', '☮️', '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐', '⛎', '♈',
        '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐', '♑', '♒', '♓', '🆔', '⚛️', '🉑', '☢️', '☣️', '📴', '📳',
        '🈶', '🈚', '🈸', '🈺', '🈷️', '✴️', '🆚', '💮', '🉐', '㊙️', '㊗️', '🈴', '🈵', '🈹', '🈲', '🅰️'
    ]
};

// Detecção de dispositivo
function detectDevice() {
    const userAgent = navigator.userAgent.toLowerCase();
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    // Detectar mobile
    isMobile = /android|webos|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent) || screenWidth <= 768;

    // Detectar tablet
    isTablet = /ipad|android(?!.*mobile)|tablet/i.test(userAgent) || (screenWidth > 768 && screenWidth <= 1024);

    console.log('📱 Dispositivo detectado:', {
        isMobile,
        isTablet,
        screenWidth,
        screenHeight,
        userAgent: userAgent.substring(0, 50) + '...'
    });

    // Aplicar classes CSS baseadas no dispositivo
    document.body.classList.toggle('mobile-device', isMobile);
    document.body.classList.toggle('tablet-device', isTablet);
    document.body.classList.toggle('desktop-device', !isMobile && !isTablet);

    // Ajustar viewport para mobile
    if (isMobile || isTablet) {
        adjustMobileViewport();
    }
}

// Ajustar viewport para dispositivos móveis
function adjustMobileViewport() {
    // Prevenir zoom no iOS
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
    }

    // Ajustar altura da tela considerando barras do navegador
    function setViewportHeight() {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }

    setViewportHeight();
    window.addEventListener('resize', setViewportHeight);
    window.addEventListener('orientationchange', () => {
        setTimeout(setViewportHeight, 100);
    });
}

// Registrar Service Worker para PWA
function registerServiceWorker() {
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
            navigator.serviceWorker.register('/sw.js')
                .then((registration) => {
                    console.log('✅ Service Worker registrado:', registration.scope);
                })
                .catch((error) => {
                    console.error('❌ Erro ao registrar Service Worker:', error);
                });
        });
    }
}

// Verificar se pode instalar PWA
function checkPWAInstall() {
    let deferredPrompt;

    window.addEventListener('beforeinstallprompt', (e) => {
        console.log('📱 PWA pode ser instalado');
        e.preventDefault();
        deferredPrompt = e;

        // Mostrar botão de instalação (opcional)
        showInstallButton(deferredPrompt);
    });

    window.addEventListener('appinstalled', () => {
        console.log('🎉 PWA instalado com sucesso!');
        deferredPrompt = null;
    });
}

// Mostrar botão de instalação
function showInstallButton(deferredPrompt) {
    // Criar botão de instalação se não existir
    let installBtn = document.getElementById('install-btn');
    if (!installBtn) {
        installBtn = document.createElement('button');
        installBtn.id = 'install-btn';
        installBtn.textContent = '📱 Instalar App';
        installBtn.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 14px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 1000;
            transition: all 0.3s ease;
        `;

        installBtn.addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`👤 Usuário ${outcome} a instalação`);
                deferredPrompt = null;
                installBtn.remove();
            }
        });

        document.body.appendChild(installBtn);

        // Remover botão após 10 segundos se não usado
        setTimeout(() => {
            if (installBtn && installBtn.parentNode) {
                installBtn.remove();
            }
        }, 10000);
    }
}

// Inicialização
document.addEventListener('DOMContentLoaded', async () => {
    detectDevice();
    setupEventListeners();
    checkExistingUser();
    registerServiceWorker();
    checkPWAInstall();
    initializeEmojiPicker();
    await checkAudioCompatibility();
});

// Inicializar emoji picker
function initializeEmojiPicker() {
    console.log('🎨 Inicializando emoji picker...');

    if (!emojiGrid) {
        console.error('❌ Emoji grid não encontrado!');
        return;
    }

    // Carregar emojis da categoria padrão
    loadEmojiCategory('smileys');
    console.log('✅ Emoji picker inicializado');
}

// Configurar event listeners
function setupEventListeners() {
    // Formulários de autenticação
    loginForm.addEventListener('submit', handleLogin);
    registerForm.addEventListener('submit', handleRegister);

    // Tabs de autenticação
    authTabButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const tabName = btn.dataset.tab;
            switchAuthTab(tabName);
        });
    });

    // Links para trocar entre login e registro
    showRegisterLink.addEventListener('click', (e) => {
        e.preventDefault();
        switchAuthTab('register');
    });

    showLoginLink.addEventListener('click', (e) => {
        e.preventDefault();
        switchAuthTab('login');
    });

    // Seleção de avatar
    avatarButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            avatarButtons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
        });
    });

    // Validação em tempo real
    setupRealTimeValidation();
    
    // Tabs da sidebar
    tabButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const tabName = btn.dataset.tab;
            switchTab(tabName);
        });
    });
    
    // Modal adicionar amigo
    addFriendBtn.addEventListener('click', () => {
        addFriendModal.classList.add('active');
    });
    
    closeModalBtn.addEventListener('click', () => {
        addFriendModal.classList.remove('active');
    });
    
    // Busca de amigos
    friendSearchInput.addEventListener('input', debounce(searchUsers, 300));
    
    // Envio de mensagens
    messageForm.addEventListener('submit', handleSendMessage);

    // Logout
    logoutBtn.addEventListener('click', handleLogout);

    // Menu mobile
    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', toggleMobileSidebar);
    }

    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', closeMobileSidebar);
    }

    // Emoji e áudio
    if (emojiBtn) {
        console.log('✅ Emoji button encontrado, adicionando listener');
        emojiBtn.addEventListener('click', toggleEmojiPicker);
    } else {
        console.error('❌ Emoji button não encontrado!');
    }

    if (audioBtn) {
        console.log('✅ Audio button encontrado, adicionando listeners');

        // Event listeners para desktop
        audioBtn.addEventListener('mousedown', (e) => {
            e.preventDefault();
            console.log('🖱️ Mouse down no botão de áudio');
            startRecording();
        });

        audioBtn.addEventListener('mouseup', (e) => {
            e.preventDefault();
            console.log('🖱️ Mouse up no botão de áudio');
            stopRecording();
        });

        audioBtn.addEventListener('mouseleave', (e) => {
            console.log('🖱️ Mouse leave no botão de áudio');
            stopRecording();
        });

        // Event listeners para mobile
        audioBtn.addEventListener('touchstart', (e) => {
            e.preventDefault();
            console.log('📱 Touch start no botão de áudio');
            startRecording();
        });

        audioBtn.addEventListener('touchend', (e) => {
            e.preventDefault();
            console.log('📱 Touch end no botão de áudio');
            stopRecording();
        });

        // Fallback: click simples para teste
        audioBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('👆 Click no botão de áudio (fallback)');
            if (!isRecording) {
                startRecording();
                // Auto-stop após 3 segundos para teste
                setTimeout(() => {
                    if (isRecording) {
                        stopRecording();
                    }
                }, 3000);
            } else {
                stopRecording();
            }
        });

    } else {
        console.error('❌ Audio button não encontrado!');
    }

    // Categorias de emoji
    if (emojiCategories.length > 0) {
        console.log(`✅ ${emojiCategories.length} categorias de emoji encontradas`);
        emojiCategories.forEach(category => {
            category.addEventListener('click', () => {
                const categoryName = category.dataset.category;
                selectEmojiCategory(categoryName);
            });
        });
    } else {
        console.error('❌ Categorias de emoji não encontradas!');
    }

    // Fechar emoji picker clicando fora
    document.addEventListener('click', (e) => {
        if (!emojiPicker.contains(e.target) && e.target !== emojiBtn) {
            closeEmojiPicker();
        }
    });

    // Fechar modal clicando fora
    addFriendModal.addEventListener('click', (e) => {
        if (e.target === addFriendModal) {
            addFriendModal.classList.remove('active');
        }
    });
}

// Verificar se já existe usuário logado
function checkExistingUser() {
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        showChatScreen();
        connectWebSocket();
    }
}

// Trocar aba de autenticação
function switchAuthTab(tabName) {
    // Atualizar botões
    authTabButtons.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tabName);
    });

    // Atualizar formulários
    authForms.forEach(form => {
        form.classList.toggle('active', form.id === `${tabName}-form`);
    });

    // Limpar erros
    clearValidationErrors();
}

// Configurar validação em tempo real
function setupRealTimeValidation() {
    const registerName = document.getElementById('register-name');
    const registerEmail = document.getElementById('register-email');
    const registerPassword = document.getElementById('register-password');
    const registerConfirmPassword = document.getElementById('register-confirm-password');

    registerName.addEventListener('blur', () => validateName());
    registerEmail.addEventListener('blur', () => validateEmail());
    registerPassword.addEventListener('blur', () => validatePassword());
    registerConfirmPassword.addEventListener('blur', () => validateConfirmPassword());
}

// Lidar com login
function handleLogin(e) {
    e.preventDefault();

    const email = document.getElementById('login-email').value.trim();
    const password = document.getElementById('login-password').value;

    if (!email || !password) {
        showNotification('Por favor, preencha todos os campos', 'error');
        return;
    }

    showLoading(true);
    loginUser(email, password);
}

// Lidar com registro
function handleRegister(e) {
    e.preventDefault();

    const name = document.getElementById('register-name').value.trim();
    const email = document.getElementById('register-email').value.trim();
    const password = document.getElementById('register-password').value;
    const confirmPassword = document.getElementById('register-confirm-password').value;
    const selectedAvatar = document.querySelector('.avatar-btn.active').dataset.avatar;

    // Validar todos os campos
    const isValid = validateName() && validateEmail() && validatePassword() && validateConfirmPassword();

    if (!isValid) {
        showNotification('Por favor, corrija os erros antes de continuar', 'error');
        return;
    }

    showLoading(true);
    registerUser(name, email, password, selectedAvatar);
}

// Funções de validação
function validateName() {
    const name = document.getElementById('register-name').value.trim();
    const errorElement = document.getElementById('name-error');

    if (!name) {
        errorElement.textContent = 'Nome é obrigatório';
        return false;
    }

    if (name.length < 2) {
        errorElement.textContent = 'Nome deve ter pelo menos 2 caracteres';
        return false;
    }

    errorElement.textContent = '';
    return true;
}

function validateEmail() {
    const email = document.getElementById('register-email').value.trim();
    const errorElement = document.getElementById('email-error');

    if (!email) {
        errorElement.textContent = 'Email é obrigatório';
        return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        errorElement.textContent = 'Email inválido';
        return false;
    }

    errorElement.textContent = '';
    return true;
}

function validatePassword() {
    const password = document.getElementById('register-password').value;
    const errorElement = document.getElementById('password-error');

    if (!password) {
        errorElement.textContent = 'Senha é obrigatória';
        return false;
    }

    if (password.length < 6) {
        errorElement.textContent = 'Senha deve ter pelo menos 6 caracteres';
        return false;
    }

    errorElement.textContent = '';
    return true;
}

function validateConfirmPassword() {
    const password = document.getElementById('register-password').value;
    const confirmPassword = document.getElementById('register-confirm-password').value;
    const errorElement = document.getElementById('confirm-password-error');

    if (!confirmPassword) {
        errorElement.textContent = 'Confirmação de senha é obrigatória';
        return false;
    }

    if (password !== confirmPassword) {
        errorElement.textContent = 'Senhas não coincidem';
        return false;
    }

    errorElement.textContent = '';
    return true;
}

function clearValidationErrors() {
    const errorElements = document.querySelectorAll('.input-error');
    errorElements.forEach(element => {
        element.textContent = '';
    });
}

// Mostrar/esconder loading
function showLoading(show) {
    if (show) {
        authLoading.classList.add('active');
    } else {
        authLoading.classList.remove('active');
    }
}

// Fazer login
async function loginUser(email, password) {
    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, password })
        });

        showLoading(false);

        if (response.ok) {
            const user = await response.json();
            currentUser = user;
            localStorage.setItem('currentUser', JSON.stringify(currentUser));

            console.log('✅ Login realizado:', currentUser);
            showNotification(`Bem-vindo de volta, ${currentUser.name}!`, 'success');
            showChatScreen();
            connectWebSocket();
        } else {
            const error = await response.json();
            showNotification(error.error || 'Erro ao fazer login', 'error');
        }
    } catch (error) {
        showLoading(false);
        console.error('❌ Erro no login:', error);
        showNotification('Erro de conexão. Tente novamente.', 'error');
    }
}

// Registrar usuário
async function registerUser(name, email, password, avatar) {
    try {
        const response = await fetch('/api/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ name, email, password, avatar })
        });

        showLoading(false);

        if (response.ok) {
            const user = await response.json();
            currentUser = user;
            localStorage.setItem('currentUser', JSON.stringify(currentUser));

            console.log('✅ Usuário registrado:', currentUser);
            showNotification(`Conta criada com sucesso! Bem-vindo, ${currentUser.name}!`, 'success');
            showChatScreen();
            connectWebSocket();
        } else {
            const error = await response.json();
            showNotification(error.error || 'Erro ao criar conta', 'error');
        }
    } catch (error) {
        showLoading(false);
        console.error('❌ Erro no registro:', error);
        showNotification('Erro de conexão. Tente novamente.', 'error');
    }
}

// Mostrar tela de chat
function showChatScreen() {
    loginScreen.classList.remove('active');
    chatScreen.classList.add('active');

    // Atualizar informações do usuário
    userAvatar.textContent = currentUser.avatar;
    userName.textContent = currentUser.name;
    userId.textContent = currentUser.username;

    // Carregar dados do usuário
    loadUserData();

    // Iniciar timer para atualizar status online a cada 30 segundos
    startOnlineStatusTimer();

    // Inicializar emoji picker após mostrar chat
    setTimeout(() => {
        console.log('🎨 Inicializando emoji picker após login...');
        initializeEmojiPickerAfterLogin();
    }, 100);
}

function initializeEmojiPickerAfterLogin() {
    // Re-buscar elementos que agora estão visíveis
    const emojiBtn = document.getElementById('emoji-btn');
    const emojiPicker = document.getElementById('emoji-picker');
    const emojiGrid = document.getElementById('emoji-grid');
    const audioBtn = document.getElementById('audio-btn');

    console.log('🔍 Elementos após login:', {
        emojiBtn: !!emojiBtn,
        emojiPicker: !!emojiPicker,
        emojiGrid: !!emojiGrid,
        audioBtn: !!audioBtn
    });

    if (emojiGrid) {
        loadEmojiCategory('smileys');
    }

    // Re-adicionar event listeners se necessário
    if (emojiBtn) {
        emojiBtn.addEventListener('click', toggleEmojiPicker);
        console.log('✅ Event listener do emoji adicionado após login');
    }

    if (audioBtn) {
        audioBtn.addEventListener('mousedown', startRecording);
        audioBtn.addEventListener('mouseup', stopRecording);
        audioBtn.addEventListener('mouseleave', stopRecording);
        audioBtn.addEventListener('touchstart', startRecording);
        audioBtn.addEventListener('touchend', stopRecording);
        console.log('✅ Event listeners do áudio adicionados após login');
    }
}

// Timer para atualizar status online
let onlineStatusTimer = null;

function startOnlineStatusTimer() {
    // Limpar timer anterior se existir
    if (onlineStatusTimer) {
        clearInterval(onlineStatusTimer);
    }

    // Atualizar a cada 30 segundos
    onlineStatusTimer = setInterval(async () => {
        if (conversations.length > 0) {
            await updateFriendsOnlineStatus();
            updateConversationsList();
        }
    }, 30000); // 30 segundos
}

function stopOnlineStatusTimer() {
    if (onlineStatusTimer) {
        clearInterval(onlineStatusTimer);
        onlineStatusTimer = null;
    }
}

// Funções para menu mobile
function toggleMobileSidebar() {
    if (sidebar && sidebarOverlay) {
        const isOpen = sidebar.classList.contains('open');

        if (isOpen) {
            closeMobileSidebar();
        } else {
            openMobileSidebar();
        }
    }
}

function openMobileSidebar() {
    if (sidebar && sidebarOverlay) {
        sidebar.classList.add('open');
        sidebarOverlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

function closeMobileSidebar() {
    if (sidebar && sidebarOverlay) {
        sidebar.classList.remove('open');
        sidebarOverlay.classList.remove('active');
        document.body.style.overflow = '';
    }
}

// Fechar sidebar ao selecionar conversa em mobile
function openConversationMobile(conversationId) {
    openConversation(conversationId);

    // Fechar sidebar em dispositivos móveis
    if (isMobile || isTablet) {
        closeMobileSidebar();
    }
}

// Funções do Emoji Picker
function toggleEmojiPicker() {
    console.log('🎭 Toggle emoji picker chamado, estado atual:', emojiPickerOpen);

    if (emojiPickerOpen) {
        closeEmojiPicker();
    } else {
        openEmojiPicker();
    }
}

function openEmojiPicker() {
    emojiPicker.classList.add('active');
    emojiPickerOpen = true;

    // Carregar emojis da categoria ativa se ainda não carregou
    const activeCategory = document.querySelector('.emoji-category.active');
    if (activeCategory && emojiGrid.children.length === 0) {
        loadEmojiCategory(activeCategory.dataset.category);
    }
}

function closeEmojiPicker() {
    emojiPicker.classList.remove('active');
    emojiPickerOpen = false;
}

function selectEmojiCategory(categoryName) {
    // Atualizar categoria ativa
    emojiCategories.forEach(cat => cat.classList.remove('active'));
    document.querySelector(`[data-category="${categoryName}"]`).classList.add('active');

    // Carregar emojis da categoria
    loadEmojiCategory(categoryName);
}

function loadEmojiCategory(categoryName) {
    console.log(`🎭 Carregando categoria: ${categoryName}`);

    const emojis = emojiDatabase[categoryName] || [];
    console.log(`📝 Emojis encontrados: ${emojis.length}`);

    if (!emojiGrid) {
        console.error('❌ Emoji grid não encontrado na loadEmojiCategory!');
        return;
    }

    emojiGrid.innerHTML = '';

    emojis.forEach(emoji => {
        const emojiElement = document.createElement('div');
        emojiElement.className = 'emoji-item';
        emojiElement.textContent = emoji;
        emojiElement.addEventListener('click', () => {
            insertEmoji(emoji);
        });
        emojiGrid.appendChild(emojiElement);
    });

    console.log(`✅ ${emojis.length} emojis carregados no grid`);
}

function insertEmoji(emoji) {
    const messageInput = document.getElementById('message-input');
    const currentValue = messageInput.value;
    const cursorPosition = messageInput.selectionStart;

    const newValue = currentValue.slice(0, cursorPosition) + emoji + currentValue.slice(cursorPosition);
    messageInput.value = newValue;

    // Manter foco no input
    messageInput.focus();
    messageInput.setSelectionRange(cursorPosition + emoji.length, cursorPosition + emoji.length);

    // Fechar picker em mobile
    if (isMobile || isTablet) {
        closeEmojiPicker();
    }
}

// Verificar dispositivos de mídia disponíveis
async function checkAvailableDevices() {
    try {
        if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
            console.log('❌ enumerateDevices não suportado');
            return false;
        }

        const devices = await navigator.mediaDevices.enumerateDevices();
        const audioInputs = devices.filter(device => device.kind === 'audioinput');

        console.log(`🎤 Dispositivos de áudio encontrados: ${audioInputs.length}`);
        audioInputs.forEach((device, index) => {
            console.log(`  ${index + 1}. ${device.label || 'Microfone ' + (index + 1)} (${device.deviceId.substring(0, 8)}...)`);
        });

        if (audioInputs.length === 0) {
            console.log('❌ Nenhum dispositivo de áudio encontrado');
            showToast('Nenhum microfone detectado no sistema', 'warning');
            return false;
        }

        return true;
    } catch (error) {
        console.error('❌ Erro ao verificar dispositivos:', error);
        return false;
    }
}

// Verificar compatibilidade de áudio
async function checkAudioCompatibility() {
    console.log('🔍 Verificando compatibilidade de áudio...');

    // Verificar HTTPS
    const isSecure = location.protocol === 'https:' || location.hostname === 'localhost';
    console.log(`🔒 Conexão segura: ${isSecure}`);

    // Verificar MediaDevices API
    const hasMediaDevices = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    console.log(`🎤 MediaDevices API: ${hasMediaDevices}`);

    // Verificar MediaRecorder API
    const hasMediaRecorder = !!(window.MediaRecorder);
    console.log(`📹 MediaRecorder API: ${hasMediaRecorder}`);

    if (!isSecure) {
        console.error('❌ Microfone requer HTTPS ou localhost');
        showToast('Microfone requer conexão segura (HTTPS)', 'error');
        return false;
    }

    if (!hasMediaDevices) {
        console.error('❌ MediaDevices API não suportada');
        showToast('Seu navegador não suporta gravação de áudio', 'error');
        return false;
    }

    if (!hasMediaRecorder) {
        console.error('❌ MediaRecorder API não suportada');
        showToast('Seu navegador não suporta gravação de áudio', 'error');
        return false;
    }

    // Verificar dispositivos disponíveis
    const hasDevices = await checkAvailableDevices();
    if (!hasDevices) {
        return false;
    }

    console.log('✅ Compatibilidade de áudio OK');
    return true;
}

// Funções de Áudio
async function startRecording() {
    console.log('🎤 Tentando iniciar gravação...');

    if (isRecording) {
        console.log('⚠️ Já está gravando');
        return;
    }

    // Verificar compatibilidade
    if (!checkAudioCompatibility()) {
        return;
    }

    try {
        // Parar áudio atual se estiver tocando
        if (currentAudio) {
            currentAudio.pause();
            currentAudio = null;
        }

        console.log('🔑 Solicitando permissão do microfone...');
        const stream = await navigator.mediaDevices.getUserMedia({
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
            }
        });

        console.log('✅ Permissão concedida, iniciando gravação');

        mediaRecorder = new MediaRecorder(stream, {
            mimeType: MediaRecorder.isTypeSupported('audio/webm') ? 'audio/webm' : 'audio/wav'
        });
        audioChunks = [];

        mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
                audioChunks.push(event.data);
                console.log(`📦 Chunk de áudio: ${event.data.size} bytes`);
            }
        };

        mediaRecorder.onstop = () => {
            console.log('⏹️ Gravação finalizada, processando áudio...');
            const mimeType = mediaRecorder.mimeType || 'audio/wav';
            const audioBlob = new Blob(audioChunks, { type: mimeType });
            console.log(`🎵 Áudio criado: ${audioBlob.size} bytes, tipo: ${mimeType}`);

            const audioUrl = URL.createObjectURL(audioBlob);
            sendAudioMessage(audioUrl, audioBlob);

            // Parar stream
            stream.getTracks().forEach(track => track.stop());
        };

        mediaRecorder.onerror = (event) => {
            console.error('❌ Erro no MediaRecorder:', event.error);
            showToast('Erro durante a gravação', 'error');
        };

        mediaRecorder.start(1000); // Chunk a cada 1 segundo
        isRecording = true;

        // Atualizar UI
        if (audioBtn) {
            audioBtn.classList.add('recording');
            audioBtn.textContent = '⏹️';
        }
        if (recordingIndicator) {
            recordingIndicator.classList.add('active');
        }

        console.log('🎤 Gravação iniciada com sucesso');
        showToast('Gravando áudio... Solte para enviar', 'info');

    } catch (error) {
        console.error('❌ Erro ao acessar microfone:', error);

        let errorMessage = 'Erro ao acessar microfone';
        let showSimulationOption = false;

        if (error.name === 'NotAllowedError') {
            errorMessage = 'Permissão de microfone negada. Clique no ícone de cadeado na barra de endereços e permita o microfone.';
        } else if (error.name === 'NotFoundError') {
            errorMessage = 'Microfone não encontrado. Isso pode acontecer em ambientes virtuais, servidores remotos ou dispositivos sem microfone.';
            showSimulationOption = true;
        } else if (error.name === 'NotSupportedError') {
            errorMessage = 'Gravação de áudio não suportada neste navegador.';
        } else if (error.name === 'NotReadableError') {
            errorMessage = 'Microfone está sendo usado por outro aplicativo.';
        } else if (error.name === 'OverconstrainedError') {
            errorMessage = 'Configurações de áudio não suportadas pelo dispositivo.';
        }

        showToast(errorMessage, 'error');

        // Oferecer modo de simulação para NotFoundError
        if (showSimulationOption) {
            setTimeout(() => {
                if (confirm('Microfone não encontrado. Deseja testar com um áudio simulado?')) {
                    simulateAudioMessage();
                }
            }, 2000);
        }

        // Resetar UI
        if (audioBtn) {
            audioBtn.classList.remove('recording');
            audioBtn.textContent = '🎤';
        }
        if (recordingIndicator) {
            recordingIndicator.classList.remove('active');
        }
    }
}

function stopRecording() {
    console.log('⏹️ Tentando parar gravação...');

    if (!isRecording) {
        console.log('⚠️ Não está gravando');
        return;
    }

    if (!mediaRecorder) {
        console.error('❌ MediaRecorder não encontrado');
        return;
    }

    try {
        if (mediaRecorder.state === 'recording') {
            mediaRecorder.stop();
            console.log('✅ Gravação parada');
        } else {
            console.log(`⚠️ MediaRecorder em estado: ${mediaRecorder.state}`);
        }
    } catch (error) {
        console.error('❌ Erro ao parar gravação:', error);
    }

    isRecording = false;

    // Atualizar UI
    if (audioBtn) {
        audioBtn.classList.remove('recording');
        audioBtn.textContent = '🎤';
    }
    if (recordingIndicator) {
        recordingIndicator.classList.remove('active');
    }

    console.log('⏹️ Gravação finalizada');
}

// Simular mensagem de áudio para teste
function simulateAudioMessage() {
    console.log('🎭 Simulando mensagem de áudio...');

    if (!activeConversation) {
        showToast('Selecione uma conversa primeiro', 'error');
        return;
    }

    // Criar um áudio simulado (tom de 440Hz por 2 segundos)
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 2);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 2);

    // Criar blob simulado
    const canvas = document.createElement('canvas');
    canvas.toBlob((blob) => {
        // Simular um blob de áudio
        const simulatedAudioBlob = new Blob(['audio-simulation'], { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(simulatedAudioBlob);

        console.log('🎵 Áudio simulado criado');
        showToast('Áudio simulado criado para teste!', 'success');

        sendAudioMessage(audioUrl, simulatedAudioBlob);
    });
}

function sendAudioMessage(audioUrl, audioBlob) {
    console.log('📤 Enviando mensagem de áudio...');

    if (!activeConversation) {
        console.error('❌ Nenhuma conversa ativa');
        showToast('Selecione uma conversa primeiro', 'error');
        return;
    }

    if (!audioBlob || audioBlob.size === 0) {
        console.error('❌ Áudio vazio ou inválido');
        showToast('Erro: áudio vazio', 'error');
        return;
    }

    console.log(`🎵 Áudio válido: ${audioBlob.size} bytes`);

    const audioId = 'audio_' + Date.now();
    const duration = 0; // Será calculado quando reproduzir

    const message = {
        id: Date.now(),
        type: 'audio',
        audioId: audioId,
        audioUrl: audioUrl,
        duration: duration,
        senderId: currentUser.id, // Corrigido: era 'sender'
        timestamp: new Date().toISOString(),
        conversationId: activeConversation,
        content: '🎤 Mensagem de áudio' // Fallback para exibição
    };

    console.log('📝 Mensagem criada:', message);

    // Adicionar à conversa local
    const conversation = conversations.find(c => c.id === activeConversation);
    if (conversation) {
        conversation.messages.push(message);
        console.log('✅ Mensagem adicionada à conversa local');
        displayMessages();
    } else {
        console.error('❌ Conversa não encontrada');
    }

    // Enviar via socket (sem o blob, apenas metadados)
    const socketMessage = {
        ...message,
        audioUrl: null // Não enviar URL local
    };

    if (socket && socket.connected) {
        socket.emit('message', socketMessage);
        console.log('📡 Mensagem enviada via socket');
    } else {
        console.error('❌ Socket não conectado');
        showToast('Erro de conexão. Tente novamente.', 'error');
    }

    showToast('Áudio enviado!', 'success');
    console.log('🎵 Mensagem de áudio enviada com sucesso');
}

function createAudioMessageElement(message) {
    const audioElement = document.createElement('div');
    audioElement.className = 'audio-message';

    const playBtn = document.createElement('button');
    playBtn.className = 'audio-play-btn';
    playBtn.textContent = '▶️';

    const waveform = document.createElement('div');
    waveform.className = 'audio-waveform';

    const progress = document.createElement('div');
    progress.className = 'audio-progress';
    waveform.appendChild(progress);

    const duration = document.createElement('span');
    duration.className = 'audio-duration';
    duration.textContent = '0:00';

    audioElement.appendChild(playBtn);
    audioElement.appendChild(waveform);
    audioElement.appendChild(duration);

    // Event listener para reproduzir
    playBtn.addEventListener('click', () => {
        playAudioMessage(message, playBtn, progress, duration);
    });

    return audioElement;
}

function playAudioMessage(message, playBtn, progress, durationElement) {
    // Parar áudio atual se estiver tocando
    if (currentAudio) {
        currentAudio.pause();
        currentAudio = null;
        // Resetar todos os botões de play
        document.querySelectorAll('.audio-play-btn').forEach(btn => {
            btn.textContent = '▶️';
        });
    }

    if (!message.audioUrl) {
        showToast('Áudio não disponível', 'error');
        return;
    }

    currentAudio = new Audio(message.audioUrl);

    currentAudio.addEventListener('loadedmetadata', () => {
        const duration = formatAudioDuration(currentAudio.duration);
        durationElement.textContent = duration;
    });

    currentAudio.addEventListener('timeupdate', () => {
        const progressPercent = (currentAudio.currentTime / currentAudio.duration) * 100;
        progress.style.width = progressPercent + '%';

        const currentTime = formatAudioDuration(currentAudio.currentTime);
        durationElement.textContent = currentTime;
    });

    currentAudio.addEventListener('ended', () => {
        playBtn.textContent = '▶️';
        progress.style.width = '0%';
        const totalDuration = formatAudioDuration(currentAudio.duration);
        durationElement.textContent = totalDuration;
        currentAudio = null;
    });

    currentAudio.addEventListener('error', () => {
        showToast('Erro ao reproduzir áudio', 'error');
        playBtn.textContent = '▶️';
        currentAudio = null;
    });

    // Reproduzir
    playBtn.textContent = '⏸️';
    currentAudio.play().catch(error => {
        console.error('❌ Erro ao reproduzir áudio:', error);
        showToast('Erro ao reproduzir áudio', 'error');
        playBtn.textContent = '▶️';
        currentAudio = null;
    });
}

function formatAudioDuration(seconds) {
    if (isNaN(seconds)) return '0:00';

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// Carregar dados do usuário (conversas e pedidos)
async function loadUserData() {
    if (!currentUser) return;

    try {
        // Carregar conversas
        const conversationsResponse = await fetch(`/api/conversations/${currentUser.id}`);
        if (conversationsResponse.ok) {
            const userConversations = await conversationsResponse.json();
            conversations = userConversations;

            // Verificar status online dos amigos
            await updateFriendsOnlineStatus();

            updateConversationsList();
            console.log('📋 Conversas carregadas:', conversations.length);
        }

        // Carregar pedidos de amizade
        const requestsResponse = await fetch(`/api/friend-requests/${currentUser.id}`);
        if (requestsResponse.ok) {
            const userRequests = await requestsResponse.json();
            friendRequests = userRequests;
            updateFriendRequestsList();
            console.log('💌 Pedidos carregados:', friendRequests.length);
        }
    } catch (error) {
        console.error('❌ Erro ao carregar dados:', error);
    }
}

// Verificar status online dos amigos
async function updateFriendsOnlineStatus() {
    if (conversations.length === 0) return;

    try {
        const friendIds = conversations.map(conv => conv.friend.id);

        const response = await fetch('/api/check-online-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ userIds: friendIds })
        });

        if (response.ok) {
            const statusList = await response.json();

            // Atualizar status nas conversas
            statusList.forEach(status => {
                const conversation = conversations.find(conv => conv.friend.id === status.userId);
                if (conversation) {
                    conversation.friend.isOnline = status.isOnline;
                    conversation.friend.lastSeen = status.lastSeen;
                }
            });

            console.log('🟢 Status online atualizado para', statusList.length, 'amigos');
        }
    } catch (error) {
        console.error('❌ Erro ao verificar status online:', error);
    }
}

// Fazer logout
function handleLogout() {
    if (confirm('Tem certeza que deseja sair?')) {
        // Parar timer de status online
        stopOnlineStatusTimer();

        // Desconectar WebSocket
        if (socket) {
            socket.disconnect();
            socket = null;
        }

        // Limpar dados locais
        localStorage.removeItem('currentUser');
        currentUser = null;
        activeConversation = null;
        onlineUsers = [];
        conversations = [];
        friendRequests = [];

        // Limpar interface
        messagesContainer.innerHTML = '';
        welcomeMessage.style.display = 'flex';
        chatArea.classList.add('hidden');

        // Voltar para tela de login
        chatScreen.classList.remove('active');
        loginScreen.classList.add('active');

        // Limpar formulários
        loginForm.reset();
        registerForm.reset();
        switchAuthTab('login');
        clearValidationErrors();

        // Atualizar listas vazias
        updateConversationsList();
        updateFriendRequestsList();

        showNotification('Logout realizado com sucesso!', 'info');
        console.log('👋 Logout realizado');
    }
}

// Conectar WebSocket
function connectWebSocket() {
    socket = io();
    
    socket.on('connect', () => {
        console.log('✅ WebSocket conectado');
        updateConnectionStatus(true);
        
        // Fazer login no WebSocket
        socket.emit('user-login', currentUser);
    });
    
    socket.on('disconnect', () => {
        console.log('❌ WebSocket desconectado');
        updateConnectionStatus(false);
    });
    
    // Eventos de usuários (removido - não precisamos mais da lista online)
    
    // Eventos de pedidos de amizade
    socket.on('friend-request-received', (request) => {
        console.log('💌 Pedido recebido:', request);
        friendRequests.push(request);
        updateFriendRequestsList();
        showNotification(`Novo pedido de amizade de ${request.senderName}!`);
    });
    
    socket.on('friend-request-sent', (data) => {
        console.log('📤 Pedido enviado:', data);
        showNotification('Pedido de amizade enviado!');
    });
    
    socket.on('friend-request-accepted', (data) => {
        console.log('✅ Pedido aceito:', data);
        // Adicionar conversa
        const newConversation = {
            id: data.conversationId,
            friend: data.friendData,
            messages: [],
            lastMessage: null
        };

        // Verificar se a conversa já existe
        const existingIndex = conversations.findIndex(c => c.id === data.conversationId);
        if (existingIndex === -1) {
            conversations.push(newConversation);
            updateConversationsList();
            showNotification(`Agora você pode conversar com ${data.friendData.name}!`, 'success');
        }
    });

    socket.on('conversation-created', (data) => {
        console.log('💬 Conversa criada:', data);
        const newConversation = {
            id: data.conversationId,
            friend: data.friendData,
            messages: [],
            lastMessage: null
        };

        // Verificar se a conversa já existe
        const existingIndex = conversations.findIndex(c => c.id === data.conversationId);
        if (existingIndex === -1) {
            conversations.push(newConversation);
            updateConversationsList();
            showNotification(`Nova conversa com ${data.friendData.name}!`, 'success');
        }
    });
    
    // Eventos de mensagens
    socket.on('message-received', (data) => {
        console.log('💬 Mensagem recebida:', data);
        addMessageToConversation(data.conversationId, data.message);
        
        if (activeConversation === data.conversationId) {
            displayMessage(data.message);
        }
    });
}

// Atualizar status de conexão
function updateConnectionStatus(connected) {
    if (connected) {
        connectionIndicator.className = 'status-indicator online';
        connectionText.textContent = 'Conectado';
    } else {
        connectionIndicator.className = 'status-indicator offline';
        connectionText.textContent = 'Desconectado';
    }
}

// Trocar aba
function switchTab(tabName) {
    // Atualizar botões
    tabButtons.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tabName);
    });

    // Atualizar conteúdo
    tabContents.forEach(content => {
        content.classList.toggle('active', content.id === `${tabName}-tab`);
    });

    // Não limpar a conversa ativa - manter o chat aberto
    // A conversa permanece visível independente da aba selecionada
}

// Buscar usuários
async function searchUsers() {
    const query = friendSearchInput.value.trim();

    if (!query) {
        searchResults.innerHTML = '';
        return;
    }

    try {
        const response = await fetch(`/api/search-users?query=${encodeURIComponent(query)}`);
        const users = await response.json();

        // Filtrar usuário atual
        const filteredUsers = users.filter(user => user.id !== currentUser.id);

        displaySearchResults(filteredUsers);
    } catch (error) {
        console.error('❌ Erro na busca:', error);
        showNotification('Erro ao buscar usuários', 'error');
    }
}

// Exibir resultados da busca
function displaySearchResults(users) {
    if (users.length === 0) {
        searchResults.innerHTML = '<p style="text-align: center; color: #666; padding: 1rem;">Nenhum usuário encontrado</p>';
        return;
    }
    
    searchResults.innerHTML = users.map(user => `
        <div class="user-item">
            <div class="user-info-item">
                <span class="user-avatar-item">${user.avatar}</span>
                <div class="user-details">
                    <div class="user-name-item">${user.name}</div>
                    <div class="user-id-item">${user.username}</div>
                </div>
            </div>
            <button class="btn-invite" onclick="sendFriendRequest('${user.id}')">
                Enviar Convite
            </button>
        </div>
    `).join('');
}

// Enviar pedido de amizade
function sendFriendRequest(receiverId) {
    if (!socket) {
        alert('Não conectado ao servidor');
        return;
    }
    
    socket.emit('send-friend-request', {
        senderId: currentUser.id,
        receiverId: receiverId,
        senderData: currentUser
    });
    
    // Fechar modal
    addFriendModal.classList.remove('active');
    friendSearchInput.value = '';
    searchResults.innerHTML = '';
}

// Funções de usuários online removidas - não são mais necessárias

// Atualizar lista de pedidos de amizade
function updateFriendRequestsList() {
    const requestsList = document.getElementById('friend-requests-list');
    
    if (friendRequests.length === 0) {
        requestsList.innerHTML = '<div class="empty-state"><p>Nenhum convite pendente</p></div>';
        return;
    }
    
    requestsList.innerHTML = friendRequests.map(request => `
        <div class="user-item">
            <div class="user-info-item">
                <span class="user-avatar-item">${request.senderAvatar}</span>
                <div class="user-details">
                    <div class="user-name-item">${request.senderName}</div>
                    <div class="user-id-item">${request.senderUsername}</div>
                </div>
            </div>
            <div style="display: flex; gap: 0.5rem;">
                <button class="btn-invite" onclick="acceptFriendRequest('${request.id}')">
                    Aceitar
                </button>
                <button class="btn-invite" style="background: #f87171;" onclick="rejectFriendRequest('${request.id}')">
                    Recusar
                </button>
            </div>
        </div>
    `).join('');
}

// Aceitar pedido de amizade
function acceptFriendRequest(requestId) {
    if (!socket) return;

    socket.emit('accept-friend-request', {
        requestId: requestId,
        userId: currentUser.id
    });

    // Remover da lista local (será atualizada pelo servidor)
    friendRequests = friendRequests.filter(req => req.id !== requestId);
    updateFriendRequestsList();

    showNotification('Pedido de amizade aceito!', 'success');
}

// Recusar pedido de amizade
function rejectFriendRequest(requestId) {
    friendRequests = friendRequests.filter(req => req.id !== requestId);
    updateFriendRequestsList();
}

// Atualizar lista de conversas
function updateConversationsList() {
    const conversationsList = document.getElementById('conversations-list');

    if (conversations.length === 0) {
        conversationsList.innerHTML = '<div class="empty-state"><p>Nenhuma conversa ainda</p><p>Adicione amigos para começar!</p></div>';
        return;
    }

    conversationsList.innerHTML = conversations.map(conv => {
        const lastMessageText = conv.lastMessage ?
            (conv.lastMessage.content.length > 30 ?
                conv.lastMessage.content.substring(0, 30) + '...' :
                conv.lastMessage.content) :
            'Nenhuma mensagem ainda';

        const isActive = activeConversation === conv.id ? 'active' : '';
        const onlineStatus = conv.friend.isOnline ? 'online' : 'offline';
        const statusIcon = conv.friend.isOnline ? '🟢' : '⚫';

        return `
            <div class="user-item conversation-item ${isActive}" onclick="openConversationMobile('${conv.id}')">
                <div class="user-info-item">
                    <span class="user-avatar-item">${conv.friend.avatar || '👤'}</span>
                    <div class="user-details">
                        <div class="user-name-item">
                            ${conv.friend.name}
                            <span class="status-icon ${onlineStatus}">${statusIcon}</span>
                        </div>
                        <div class="user-id-item">${conv.friend.username}</div>
                        <div class="last-message">${lastMessageText}</div>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// Abrir conversa
function openConversation(conversationId) {
    activeConversation = conversationId;
    const conversation = conversations.find(c => c.id === conversationId);

    if (!conversation) {
        console.error('❌ Conversa não encontrada:', conversationId);
        return;
    }

    // Mostrar área de chat
    welcomeMessage.style.display = 'none';
    chatArea.classList.remove('hidden');

    // Atualizar header da conversa
    document.getElementById('conversation-avatar').textContent = conversation.friend.avatar || '👤';
    document.getElementById('conversation-name').textContent = conversation.friend.name;

    const statusText = conversation.friend.isOnline ? 'online' : 'offline';
    const statusElement = document.getElementById('conversation-status');
    statusElement.textContent = statusText;
    statusElement.className = `conversation-status ${statusText}`;

    // Carregar mensagens
    loadMessages(conversationId);

    console.log('💬 Conversa aberta:', conversation.friend.name);
}

// Carregar mensagens
function loadMessages(conversationId) {
    const conversation = conversations.find(c => c.id === conversationId);
    if (!conversation) return;
    
    messagesContainer.innerHTML = '';
    
    if (conversation.messages.length === 0) {
        messagesContainer.innerHTML = '<div style="text-align: center; color: #666; padding: 2rem;">Nenhuma mensagem ainda. Comece a conversar!</div>';
        return;
    }
    
    conversation.messages.forEach(message => {
        displayMessage(message);
    });
    
    // Scroll para baixo
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Exibir mensagem
function displayMessage(message) {
    const isOwn = message.senderId === currentUser.id;
    const messageTime = new Date(message.timestamp).toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
    });
    
    const messageElement = document.createElement('div');
    messageElement.className = `message ${isOwn ? 'own' : ''}`;

    const messageAvatar = document.createElement('div');
    messageAvatar.className = 'message-avatar';
    messageAvatar.textContent = isOwn ? currentUser.avatar : '👤';

    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';

    const messageText = document.createElement('div');
    messageText.className = 'message-text';

    // Verificar tipo de mensagem
    if (message.type === 'audio') {
        const audioElement = createAudioMessageElement(message);
        messageText.appendChild(audioElement);
    } else {
        messageText.textContent = message.content;
    }

    const messageTimeElement = document.createElement('div');
    messageTimeElement.className = 'message-time';
    messageTimeElement.textContent = messageTime;

    messageContent.appendChild(messageText);
    messageContent.appendChild(messageTimeElement);

    messageElement.appendChild(messageAvatar);
    messageElement.appendChild(messageContent);

    messagesContainer.appendChild(messageElement);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Lidar com envio de mensagem
function handleSendMessage(e) {
    e.preventDefault();
    
    const content = messageInput.value.trim();
    if (!content || !activeConversation || !socket) return;
    
    const conversation = conversations.find(c => c.id === activeConversation);
    if (!conversation) return;
    
    // Enviar via WebSocket
    socket.emit('send-message', {
        conversationId: activeConversation,
        senderId: currentUser.id,
        content: content,
        receiverId: conversation.friend.id
    });
    
    // Limpar input
    messageInput.value = '';
}

// Adicionar mensagem à conversa
function addMessageToConversation(conversationId, message) {
    const conversation = conversations.find(c => c.id === conversationId);
    if (conversation) {
        conversation.messages.push(message);
        conversation.lastMessage = message;

        // Atualizar lista de conversas para mostrar última mensagem
        updateConversationsList();

        console.log('💬 Mensagem adicionada à conversa:', conversationId);
    } else {
        console.error('❌ Conversa não encontrada para adicionar mensagem:', conversationId);
    }
}

// Mostrar notificação
function showNotification(message, type = 'success') {
    // Definir cores baseadas no tipo
    const colors = {
        success: '#4ade80',
        error: '#f87171',
        warning: '#fbbf24',
        info: '#60a5fa'
    };

    // Criar elemento de notificação
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors[type] || colors.success};
        color: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        animation: slideIn 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    notification.textContent = message;
    
    // Adicionar CSS da animação
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(notification);
    
    // Remover após 3 segundos
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Função debounce
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

console.log('✅ Chat App carregado!');
