<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste do Servidor de Sincronização</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .user-item { padding: 8px; border-bottom: 1px solid #eee; }
    </style>
</head>
<body>
    <h1>🔄 Teste do Servidor de Sincronização</h1>
    
    <div class="section">
        <h2>Status do Servidor</h2>
        <button onclick="checkServerHealth()">🏥 Verificar Saúde do Servidor</button>
        <div id="serverStatus">Clique para verificar</div>
    </div>

    <div class="section">
        <h2>Controles</h2>
        <button onclick="loadUsers()">📋 Carregar Usuários</button>
        <button onclick="createTestUser()">👤 Criar Usuário de Teste</button>
        <button onclick="clearAllUsers()">🗑️ Limpar Todos os Usuários</button>
    </div>

    <div class="section">
        <h2>Usuários no Servidor</h2>
        <div id="usersList">Clique em "Carregar Usuários" para ver</div>
    </div>

    <div class="section">
        <h2>Logs</h2>
        <div id="logs" style="height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 3px;"></div>
    </div>

    <script>
        const API_URL = 'http://localhost:3001/api';
        
        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logsDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(message);
        }

        async function checkServerHealth() {
            const statusDiv = document.getElementById('serverStatus');
            try {
                const response = await fetch(`${API_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.innerHTML = `<span class="success">✅ Servidor online - ${data.timestamp}</span>`;
                    log('Servidor está online', 'success');
                } else {
                    statusDiv.innerHTML = `<span class="error">❌ Servidor retornou erro: ${response.status}</span>`;
                    log(`Servidor retornou erro: ${response.status}`, 'error');
                }
            } catch (error) {
                statusDiv.innerHTML = `<span class="error">❌ Servidor offline ou inacessível</span>`;
                log(`Erro ao conectar com servidor: ${error.message}`, 'error');
            }
        }

        async function loadUsers() {
            const usersDiv = document.getElementById('usersList');
            try {
                log('Carregando usuários do servidor...');
                const response = await fetch(`${API_URL}/users`);
                if (response.ok) {
                    const data = await response.json();
                    log(`${data.users.length} usuários carregados`, 'success');
                    
                    if (data.users.length === 0) {
                        usersDiv.innerHTML = '<p>Nenhum usuário encontrado</p>';
                    } else {
                        let html = `<p><strong>Total:</strong> ${data.users.length} usuários (Contador: ${data.counter})</p>`;
                        data.users.forEach(user => {
                            html += `
                                <div class="user-item">
                                    <strong>${user.name}</strong> (${user.displayUsername || user.username})
                                    <br><small>Login: ${user.username} | ID: ${user.sequentialId || 'N/A'}</small>
                                </div>
                            `;
                        });
                        usersDiv.innerHTML = html;
                    }
                } else {
                    usersDiv.innerHTML = '<p class="error">Erro ao carregar usuários</p>';
                    log(`Erro ao carregar usuários: ${response.status}`, 'error');
                }
            } catch (error) {
                usersDiv.innerHTML = '<p class="error">Erro de conexão</p>';
                log(`Erro ao carregar usuários: ${error.message}`, 'error');
            }
        }

        async function createTestUser() {
            try {
                const username = `test_${Date.now()}`;
                const userData = {
                    name: `Usuário Teste ${Math.floor(Math.random() * 1000)}`,
                    username: username,
                    password: '1234',
                    avatar: '😊',
                    status: 'online'
                };

                log(`Criando usuário: ${userData.name} (${username})`);
                
                const response = await fetch(`${API_URL}/users`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(userData)
                });

                if (response.ok) {
                    const savedUser = await response.json();
                    log(`Usuário criado: ${savedUser.name} - ${savedUser.displayUsername}`, 'success');
                    loadUsers(); // Reload users list
                } else {
                    const error = await response.json();
                    log(`Erro ao criar usuário: ${error.error}`, 'error');
                }
            } catch (error) {
                log(`Erro ao criar usuário: ${error.message}`, 'error');
            }
        }

        async function clearAllUsers() {
            if (!confirm('Tem certeza que deseja deletar TODOS os usuários do servidor?')) {
                return;
            }

            try {
                log('Deletando todos os usuários...');
                const response = await fetch(`${API_URL}/users`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    log('Todos os usuários foram deletados', 'success');
                    loadUsers(); // Reload users list
                } else {
                    log(`Erro ao deletar usuários: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`Erro ao deletar usuários: ${error.message}`, 'error');
            }
        }

        // Auto-check server health on load
        window.onload = function() {
            checkServerHealth();
            loadUsers();
        };
    </script>
</body>
</html>
