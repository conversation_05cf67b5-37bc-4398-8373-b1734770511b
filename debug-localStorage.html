<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug localStorage</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>🔍 Debug localStorage - Chat App</h1>
    
    <div class="section">
        <h2>Controles</h2>
        <button onclick="showAllData()">📊 Mostrar Todos os Dados</button>
        <button onclick="createTestUser()">👤 Criar <PERSON></button>
        <button onclick="clearAll()">🗑️ Limpar Tudo</button>
        <button onclick="testMigration()">🔄 Testar Migração</button>
    </div>

    <div class="section">
        <h2>Status</h2>
        <div id="status">Pronto</div>
    </div>

    <div class="section">
        <h2>Dados do localStorage</h2>
        <pre id="data">Clique em "Mostrar Todos os Dados" para ver</pre>
    </div>

    <script>
        const SHARED_USERS_KEY = 'shared_registered_users_db';
        const SHARED_COUNTER_KEY = 'shared_user_counter';

        function log(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            statusDiv.innerHTML = `<span class="${className}">${message}</span>`;
            console.log(message);
        }

        function showAllData() {
            const dataDiv = document.getElementById('data');
            let output = '=== TODOS OS DADOS DO LOCALSTORAGE ===\n\n';
            
            // Show all localStorage keys
            output += `Total de chaves: ${localStorage.length}\n\n`;
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                output += `[${i}] ${key}:\n`;
                
                try {
                    const parsed = JSON.parse(value);
                    output += JSON.stringify(parsed, null, 2);
                } catch (e) {
                    output += value;
                }
                output += '\n\n---\n\n';
            }

            // Specific checks
            output += '=== VERIFICAÇÕES ESPECÍFICAS ===\n\n';
            
            const sharedUsers = localStorage.getItem(SHARED_USERS_KEY);
            output += `Usuários compartilhados (${SHARED_USERS_KEY}):\n`;
            if (sharedUsers) {
                try {
                    const users = JSON.parse(sharedUsers);
                    output += `${users.length} usuários encontrados\n`;
                    users.forEach((user, index) => {
                        output += `  ${index + 1}. ${user.name} (${user.username}) - ${user.displayUsername}\n`;
                    });
                } catch (e) {
                    output += `ERRO: ${e.message}\n`;
                }
            } else {
                output += 'NENHUM\n';
            }

            output += '\n';
            const counter = localStorage.getItem(SHARED_COUNTER_KEY);
            output += `Contador (${SHARED_COUNTER_KEY}): ${counter || 'NENHUM'}\n\n`;

            // Check for old format users
            output += 'Usuários no formato antigo:\n';
            let oldUsers = 0;
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('registered_user_')) {
                    oldUsers++;
                    output += `  - ${key}\n`;
                }
            }
            if (oldUsers === 0) {
                output += '  NENHUM\n';
            }

            dataDiv.textContent = output;
            log(`Dados carregados. ${localStorage.length} chaves no total.`, 'success');
        }

        function createTestUser() {
            const username = `test_${Date.now()}`;
            const name = `Usuário Teste ${Math.floor(Math.random() * 1000)}`;
            
            // Get current counter
            let counter = parseInt(localStorage.getItem(SHARED_COUNTER_KEY) || '0');
            counter++;
            
            const userData = {
                id: `user_${Date.now()}`,
                name: name,
                username: username,
                password: '1234',
                displayUsername: `#${counter}`,
                sequentialId: counter,
                avatar: '😊',
                status: 'online',
                createdAt: new Date().toISOString(),
            };

            // Save to shared database
            const existingUsers = JSON.parse(localStorage.getItem(SHARED_USERS_KEY) || '[]');
            existingUsers.push(userData);
            localStorage.setItem(SHARED_USERS_KEY, JSON.stringify(existingUsers));
            localStorage.setItem(SHARED_COUNTER_KEY, counter.toString());

            log(`Usuário criado: ${name} (${username}) - ${userData.displayUsername}`, 'success');
            showAllData();
        }

        function clearAll() {
            if (confirm('Tem certeza que deseja limpar TODOS os dados?')) {
                localStorage.clear();
                log('Todos os dados foram limpos', 'success');
                showAllData();
            }
        }

        function testMigration() {
            // Create some old format users first
            const oldUser1 = {
                id: 'user_old_1',
                name: 'Usuário Antigo 1',
                username: 'old1',
                password: '1234',
                displayUsername: '#1',
                sequentialId: 1,
                avatar: '😊',
                status: 'online',
                createdAt: new Date().toISOString(),
            };

            const oldUser2 = {
                id: 'user_old_2',
                name: 'Usuário Antigo 2',
                username: 'old2',
                password: '1234',
                displayUsername: '#2',
                sequentialId: 2,
                avatar: '😊',
                status: 'online',
                createdAt: new Date().toISOString(),
            };

            // Save in old format
            localStorage.setItem('registered_user_old1', JSON.stringify(oldUser1));
            localStorage.setItem('registered_user_old2', JSON.stringify(oldUser2));
            localStorage.setItem('lastUserId', '2');

            log('Usuários no formato antigo criados. Agora testando migração...', 'info');

            // Now test migration
            setTimeout(() => {
                // Simulate migration
                const sharedData = localStorage.getItem(SHARED_USERS_KEY);
                if (sharedData) {
                    log('Dados já estão na base compartilhada', 'info');
                    return;
                }

                const users = [];
                const keysToRemove = [];
                
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('registered_user_')) {
                        try {
                            const userData = JSON.parse(localStorage.getItem(key) || '');
                            users.push(userData);
                            keysToRemove.push(key);
                        } catch (error) {
                            console.error('Error parsing user data during migration:', error);
                        }
                    }
                }

                if (users.length > 0) {
                    localStorage.setItem(SHARED_USERS_KEY, JSON.stringify(users));
                    keysToRemove.forEach(key => localStorage.removeItem(key));
                    log(`Migração concluída: ${users.length} usuários migrados`, 'success');
                } else {
                    log('Nenhum usuário encontrado para migrar', 'info');
                }

                showAllData();
            }, 1000);
        }

        // Auto-load on page load
        window.onload = showAllData;
    </script>
</body>
</html>
