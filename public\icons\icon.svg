<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="512" height="512" rx="80" fill="url(#gradient)"/>
  
  <!-- Chat bubble 1 -->
  <path d="M120 180 Q120 150 150 150 L300 150 Q330 150 330 180 L330 220 Q330 250 300 250 L180 250 L150 280 L150 250 Q120 250 120 220 Z" fill="white" opacity="0.9"/>
  
  <!-- Chat bubble 2 -->
  <path d="M182 280 Q182 310 212 310 L362 310 Q392 310 392 280 L392 240 Q392 210 362 210 L242 210 L212 180 L212 210 Q182 210 182 240 Z" fill="white" opacity="0.7"/>
  
  <!-- Chat icon -->
  <circle cx="225" cy="200" r="8" fill="#667eea"/>
  <circle cx="255" cy="200" r="8" fill="#667eea"/>
  <circle cx="285" cy="200" r="8" fill="#667eea"/>
  
  <!-- Chat icon 2 -->
  <circle cx="242" cy="260" r="8" fill="#764ba2"/>
  <circle cx="272" cy="260" r="8" fill="#764ba2"/>
  <circle cx="302" cy="260" r="8" fill="#764ba2"/>
  
  <!-- Title -->
  <text x="256" y="380" font-family="Arial, sans-serif" font-size="48" font-weight="bold" text-anchor="middle" fill="white">Chat</text>
  <text x="256" y="420" font-family="Arial, sans-serif" font-size="32" text-anchor="middle" fill="white" opacity="0.8">JS</text>
</svg>
