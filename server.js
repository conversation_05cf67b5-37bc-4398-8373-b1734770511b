const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());

// Dados em memória
let users = [];
let userCounter = 0;
let onlineUsers = new Map();
let conversations = new Map();
let messages = new Map();
let friendRequests = new Map();

console.log('🚀 Servidor de Chat Iniciando...');

// Conexões WebSocket
io.on('connection', (socket) => {
  console.log(`👤 Usuário conectado: ${socket.id}`);

  // Login do usuário
  socket.on('user-login', (userData) => {
    console.log(`🔐 Login: ${userData.name}`);
    
    // Adicionar aos usuários online
    onlineUsers.set(userData.id, {
      socketId: socket.id,
      userData: userData,
      lastSeen: Date.now()
    });

    // Entrar na sala pessoal
    socket.join(`user_${userData.id}`);
    
    // Notificar outros usuários
    socket.broadcast.emit('user-online', {
      id: userData.id,
      name: userData.name,
      username: userData.username,
      avatar: userData.avatar,
      status: 'online'
    });

    // Enviar lista de usuários online
    const onlineList = Array.from(onlineUsers.values())
      .filter(user => user.userData.id !== userData.id)
      .map(user => ({
        id: user.userData.id,
        name: user.userData.name,
        username: user.userData.username,
        avatar: user.userData.avatar,
        status: 'online'
      }));

    socket.emit('online-users-list', onlineList);

    // Enviar pedidos de amizade pendentes
    const userRequests = friendRequests.get(userData.id) || [];
    socket.emit('friend-requests-update', userRequests);
  });

  // Enviar pedido de amizade
  socket.on('send-friend-request', (data) => {
    const { senderId, receiverId, senderData } = data;
    console.log(`💌 Pedido de amizade: ${senderData.name} -> ${receiverId}`);

    const friendRequest = {
      id: `req_${Date.now()}_${Math.random()}`,
      senderId: senderId,
      receiverId: receiverId,
      senderName: senderData.name,
      senderUsername: senderData.username,
      senderAvatar: senderData.avatar,
      timestamp: new Date().toISOString(),
      status: 'pending'
    };

    // Adicionar aos pedidos do receptor
    if (!friendRequests.has(receiverId)) {
      friendRequests.set(receiverId, []);
    }
    friendRequests.get(receiverId).push(friendRequest);

    // Notificar receptor se online
    io.to(`user_${receiverId}`).emit('friend-request-received', friendRequest);
    
    // Confirmar para o remetente
    socket.emit('friend-request-sent', { success: true, receiverId });
  });

  // Aceitar pedido de amizade
  socket.on('accept-friend-request', (data) => {
    const { requestId, userId } = data;
    console.log(`✅ Pedido aceito: ${requestId}`);

    const userRequests = friendRequests.get(userId) || [];
    const requestIndex = userRequests.findIndex(req => req.id === requestId);
    
    if (requestIndex !== -1) {
      const request = userRequests[requestIndex];
      userRequests.splice(requestIndex, 1);

      // Criar conversa
      const conversationId = `conv_${Date.now()}_${request.senderId}_${userId}`;
      const conversation = {
        id: conversationId,
        participants: [request.senderId, userId],
        createdAt: new Date().toISOString(),
        lastMessage: null
      };

      conversations.set(conversationId, conversation);
      messages.set(conversationId, []);

      // Notificar ambos usuários
      io.to(`user_${request.senderId}`).emit('friend-request-accepted', {
        conversationId,
        friendData: {
          id: userId,
          name: 'Amigo',
          username: 'amigo'
        }
      });

      io.to(`user_${userId}`).emit('conversation-created', {
        conversationId,
        friendData: {
          id: request.senderId,
          name: request.senderName,
          username: request.senderUsername
        }
      });

      // Atualizar pedidos
      io.to(`user_${userId}`).emit('friend-requests-update', userRequests);
    }
  });

  // Enviar mensagem
  socket.on('send-message', (data) => {
    const { conversationId, senderId, content, receiverId } = data;
    console.log(`💬 Mensagem: ${content.substring(0, 50)}...`);

    const message = {
      id: `msg_${Date.now()}_${Math.random()}`,
      senderId: senderId,
      receiverId: receiverId,
      content: content,
      timestamp: new Date().toISOString(),
      type: 'text'
    };

    // Armazenar mensagem
    if (!messages.has(conversationId)) {
      messages.set(conversationId, []);
    }
    messages.get(conversationId).push(message);

    // Atualizar conversa
    if (conversations.has(conversationId)) {
      const conversation = conversations.get(conversationId);
      conversation.lastMessage = message;
      conversation.updatedAt = new Date().toISOString();
    }

    // Enviar para participantes
    const conversation = conversations.get(conversationId);
    if (conversation) {
      conversation.participants.forEach(participantId => {
        io.to(`user_${participantId}`).emit('message-received', {
          conversationId,
          message
        });
      });
    }
  });

  // Desconexão
  socket.on('disconnect', () => {
    console.log(`👋 Usuário desconectado: ${socket.id}`);
    
    // Remover dos usuários online
    let disconnectedUserId = null;
    for (const [userId, userInfo] of onlineUsers.entries()) {
      if (userInfo.socketId === socket.id) {
        disconnectedUserId = userId;
        onlineUsers.delete(userId);
        break;
      }
    }

    // Notificar outros usuários
    if (disconnectedUserId) {
      socket.broadcast.emit('user-offline', { id: disconnectedUserId });
    }
  });
});

// API para gerenciar usuários
app.get('/api/users', (req, res) => {
  res.json({
    users: users,
    counter: userCounter
  });
});

app.post('/api/users', (req, res) => {
  const userData = req.body;
  
  // Verificar se usuário já existe
  const existingUser = users.find(u => u.username === userData.username);
  if (existingUser) {
    return res.status(400).json({ error: 'Nome de usuário já existe' });
  }
  
  // Gerar ID sequencial
  userCounter++;
  userData.sequentialId = userCounter;
  userData.username = `#${userCounter}`;
  userData.id = userData.id || `user_${Date.now()}`;
  userData.createdAt = userData.createdAt || new Date().toISOString();
  
  // Adicionar usuário
  users.push(userData);
  
  console.log(`✅ Novo usuário: ${userData.name} (${userData.username})`);
  res.json(userData);
});

app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    onlineUsers: onlineUsers.size,
    totalUsers: users.length,
    conversations: conversations.size
  });
});

// Servir página principal
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Iniciar servidor
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Servidor rodando na porta ${PORT}`);
  console.log(`🌐 Acesse: http://localhost:${PORT}`);
  console.log('🔄 Chat em tempo real ativo!');
});

// Encerramento gracioso
process.on('SIGINT', () => {
  console.log('\n🛑 Encerrando servidor...');
  io.close();
  server.close();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Encerrando servidor...');
  io.close();
  server.close();
  process.exit(0);
});
