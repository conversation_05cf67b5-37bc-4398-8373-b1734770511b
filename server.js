const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());

// Dados em memória
let users = [];
let userCounter = 0;
let onlineUsers = new Map();
let conversations = new Map();
let messages = new Map();
let friendRequests = new Map();
let friendships = new Map(); // Para armazenar amizades aceitas

console.log('🚀 Servidor de Chat Iniciando...');

// Conexões WebSocket
io.on('connection', (socket) => {
  console.log(`👤 Usuário conectado: ${socket.id}`);

  // Login do usuário
  socket.on('user-login', (userData) => {
    console.log(`🔐 Login: ${userData.name}`);
    
    // Adicionar aos usuários online
    onlineUsers.set(userData.id, {
      socketId: socket.id,
      userData: userData,
      lastSeen: Date.now()
    });

    // Entrar na sala pessoal
    socket.join(`user_${userData.id}`);
    
    // Notificar outros usuários
    socket.broadcast.emit('user-online', {
      id: userData.id,
      name: userData.name,
      username: userData.username,
      avatar: userData.avatar,
      status: 'online'
    });

    // Enviar lista de usuários online
    const onlineList = Array.from(onlineUsers.values())
      .filter(user => user.userData.id !== userData.id)
      .map(user => ({
        id: user.userData.id,
        name: user.userData.name,
        username: user.userData.username,
        avatar: user.userData.avatar,
        status: 'online'
      }));

    socket.emit('online-users-list', onlineList);

    // Enviar pedidos de amizade pendentes
    const userRequests = friendRequests.get(userData.id) || [];
    socket.emit('friend-requests-update', userRequests);
  });

  // Enviar pedido de amizade
  socket.on('send-friend-request', (data) => {
    const { senderId, receiverId, senderData } = data;
    console.log(`💌 Pedido de amizade: ${senderData.name} -> ${receiverId}`);

    const friendRequest = {
      id: `req_${Date.now()}_${Math.random()}`,
      senderId: senderId,
      receiverId: receiverId,
      senderName: senderData.name,
      senderUsername: senderData.username,
      senderAvatar: senderData.avatar,
      timestamp: new Date().toISOString(),
      status: 'pending'
    };

    // Armazenar pedido por ID único
    friendRequests.set(friendRequest.id, friendRequest);

    // Notificar receptor se online
    io.to(`user_${receiverId}`).emit('friend-request-received', friendRequest);

    // Confirmar para o remetente
    socket.emit('friend-request-sent', { success: true, receiverId });

    console.log(`📋 Pedido criado: ${friendRequest.id}`);
  });

  // Aceitar pedido de amizade
  socket.on('accept-friend-request', (data) => {
    const { requestId, userId } = data;
    console.log(`✅ Tentando aceitar pedido: ${requestId} por usuário: ${userId}`);
    console.log(`📋 Total de pedidos: ${friendRequests.size}`);

    // Buscar o pedido específico
    const foundRequest = friendRequests.get(requestId);
    console.log(`🔍 Pedido encontrado:`, foundRequest);

    if (foundRequest && foundRequest.receiverId === userId) {
      console.log(`✅ Pedido válido! Criando conversa...`);

      // Remover o pedido
      friendRequests.delete(requestId);

      // Buscar dados completos dos usuários
      const senderUser = users.find(u => u.id === foundRequest.senderId);
      const receiverUser = users.find(u => u.id === userId);

      console.log(`👤 Remetente:`, senderUser?.name);
      console.log(`👤 Receptor:`, receiverUser?.name);

      if (senderUser && receiverUser) {
        // Criar conversa
        const conversationId = `conv_${Date.now()}`;
        const conversation = {
          id: conversationId,
          participants: [foundRequest.senderId, userId],
          createdAt: new Date().toISOString(),
          lastMessage: null
        };

        conversations.set(conversationId, conversation);
        messages.set(conversationId, []);

        console.log(`💬 Conversa criada: ${conversationId}`);

        // Dados para o remetente (quem enviou o pedido)
        const dataForSender = {
          conversationId,
          friendData: {
            id: receiverUser.id,
            name: receiverUser.name,
            username: receiverUser.username,
            avatar: receiverUser.avatar
          }
        };

        // Dados para o receptor (quem aceitou)
        const dataForReceiver = {
          conversationId,
          friendData: {
            id: senderUser.id,
            name: senderUser.name,
            username: senderUser.username,
            avatar: senderUser.avatar
          }
        };

        // Notificar ambos usuários
        io.to(`user_${foundRequest.senderId}`).emit('conversation-created', dataForSender);
        io.to(`user_${userId}`).emit('conversation-created', dataForReceiver);

        console.log(`🎉 Notificações enviadas para ambos usuários`);
        console.log(`📊 Total de conversas: ${conversations.size}`);
      } else {
        console.log('❌ Usuários não encontrados');
      }
    } else {
      console.log('❌ Pedido não encontrado ou inválido');
      console.log('📋 Pedidos disponíveis:', Array.from(friendRequests.keys()));
    }
  });

  // Enviar mensagem
  socket.on('send-message', (data) => {
    const { conversationId, senderId, content, receiverId } = data;
    console.log(`💬 Mensagem: ${content.substring(0, 50)}...`);

    const message = {
      id: `msg_${Date.now()}_${Math.random()}`,
      senderId: senderId,
      receiverId: receiverId,
      content: content,
      timestamp: new Date().toISOString(),
      type: 'text'
    };

    // Armazenar mensagem
    if (!messages.has(conversationId)) {
      messages.set(conversationId, []);
    }
    messages.get(conversationId).push(message);

    // Atualizar conversa
    if (conversations.has(conversationId)) {
      const conversation = conversations.get(conversationId);
      conversation.lastMessage = message;
      conversation.updatedAt = new Date().toISOString();
    }

    // Enviar para participantes
    const conversation = conversations.get(conversationId);
    if (conversation) {
      conversation.participants.forEach(participantId => {
        io.to(`user_${participantId}`).emit('message-received', {
          conversationId,
          message
        });
      });
    }
  });

  // Desconexão
  socket.on('disconnect', () => {
    console.log(`👋 Usuário desconectado: ${socket.id}`);
    
    // Remover dos usuários online
    let disconnectedUserId = null;
    for (const [userId, userInfo] of onlineUsers.entries()) {
      if (userInfo.socketId === socket.id) {
        disconnectedUserId = userId;
        onlineUsers.delete(userId);
        break;
      }
    }

    // Notificar outros usuários
    if (disconnectedUserId) {
      socket.broadcast.emit('user-offline', { id: disconnectedUserId });
    }
  });
});

// API para gerenciar usuários
app.get('/api/users', (req, res) => {
  res.json({
    users: users,
    counter: userCounter
  });
});

// Registrar novo usuário
app.post('/api/register', (req, res) => {
  const { name, email, password, avatar } = req.body;

  // Validações
  if (!name || !email || !password) {
    return res.status(400).json({ error: 'Nome, email e senha são obrigatórios' });
  }

  if (name.length < 2) {
    return res.status(400).json({ error: 'Nome deve ter pelo menos 2 caracteres' });
  }

  if (password.length < 6) {
    return res.status(400).json({ error: 'Senha deve ter pelo menos 6 caracteres' });
  }

  // Verificar se email já existe
  const existingUser = users.find(u => u.email === email);
  if (existingUser) {
    return res.status(400).json({ error: 'Este email já está cadastrado' });
  }

  // Gerar ID sequencial
  userCounter++;
  const userData = {
    id: `user_${Date.now()}_${userCounter}`,
    name: name.trim(),
    email: email.toLowerCase().trim(),
    password: password, // Em produção, usar hash
    avatar: avatar || '😊',
    username: `#${userCounter}`,
    sequentialId: userCounter,
    createdAt: new Date().toISOString(),
    lastLogin: new Date().toISOString()
  };

  // Adicionar usuário
  users.push(userData);

  console.log(`✅ Novo usuário registrado: ${userData.name} (${userData.username})`);

  // Retornar dados sem senha
  const { password: _, ...userResponse } = userData;
  res.json(userResponse);
});

// Login de usuário
app.post('/api/login', (req, res) => {
  const { email, password } = req.body;

  // Validações
  if (!email || !password) {
    return res.status(400).json({ error: 'Email e senha são obrigatórios' });
  }

  // Buscar usuário
  const user = users.find(u => u.email === email.toLowerCase().trim());
  if (!user) {
    return res.status(401).json({ error: 'Email ou senha incorretos' });
  }

  // Verificar senha
  if (user.password !== password) {
    return res.status(401).json({ error: 'Email ou senha incorretos' });
  }

  // Atualizar último login
  user.lastLogin = new Date().toISOString();

  console.log(`🔐 Login realizado: ${user.name} (${user.username})`);

  // Retornar dados sem senha
  const { password: _, ...userResponse } = user;
  res.json(userResponse);
});

// Verificar se email existe
app.post('/api/check-email', (req, res) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({ error: 'Email é obrigatório' });
  }

  const exists = users.some(u => u.email === email.toLowerCase().trim());
  res.json({ exists });
});

// Buscar usuários (para adicionar amigos)
app.get('/api/search-users', (req, res) => {
  const { query } = req.query;

  if (!query) {
    return res.json([]);
  }

  const filteredUsers = users.filter(user => {
    const queryLower = query.toLowerCase();
    const nameMatch = user.name.toLowerCase().includes(queryLower);
    const usernameMatch = user.username.toLowerCase().includes(queryLower);
    const emailMatch = user.email.toLowerCase().includes(queryLower);

    return nameMatch || usernameMatch || emailMatch;
  }).map(user => ({
    id: user.id,
    name: user.name,
    username: user.username,
    avatar: user.avatar,
    email: user.email
  }));

  res.json(filteredUsers);
});

// Buscar conversas do usuário
app.get('/api/conversations/:userId', (req, res) => {
  const { userId } = req.params;

  const userConversations = [];

  // Buscar todas as conversas onde o usuário participa
  conversations.forEach((conversation, conversationId) => {
    if (conversation.participants.includes(userId)) {
      // Encontrar o outro participante
      const otherUserId = conversation.participants.find(id => id !== userId);
      const otherUser = users.find(u => u.id === otherUserId);

      if (otherUser) {
        // Buscar mensagens da conversa
        const conversationMessages = messages.get(conversationId) || [];

        userConversations.push({
          id: conversationId,
          friend: {
            id: otherUser.id,
            name: otherUser.name,
            username: otherUser.username,
            avatar: otherUser.avatar
          },
          messages: conversationMessages,
          lastMessage: conversationMessages[conversationMessages.length - 1] || null
        });
      }
    }
  });

  res.json(userConversations);
});

// Buscar pedidos de amizade do usuário
app.get('/api/friend-requests/:userId', (req, res) => {
  const { userId } = req.params;

  const userRequests = [];

  friendRequests.forEach((request, requestId) => {
    if (request.receiverId === userId && request.status === 'pending') {
      userRequests.push({
        id: requestId,
        senderId: request.senderId,
        senderName: request.senderName,
        senderUsername: request.senderUsername,
        senderAvatar: request.senderAvatar
      });
    }
  });

  res.json(userRequests);
});

app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    onlineUsers: onlineUsers.size,
    totalUsers: users.length,
    conversations: conversations.size
  });
});

// Servir página principal
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Iniciar servidor
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Servidor rodando na porta ${PORT}`);
  console.log(`🌐 Acesse: http://localhost:${PORT}`);
  console.log('🔄 Chat em tempo real ativo!');
});

// Encerramento gracioso
process.on('SIGINT', () => {
  console.log('\n🛑 Encerrando servidor...');
  io.close();
  server.close();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Encerrando servidor...');
  io.close();
  server.close();
  process.exit(0);
});
