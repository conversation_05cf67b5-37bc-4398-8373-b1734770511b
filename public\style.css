/* Reset e Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
}

/* Telas */
.screen {
    display: none;
    height: 100vh;
}

.screen.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Tela de Autenticação */
.auth-container {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    text-align: center;
    max-width: 450px;
    width: 90%;
    position: relative;
}

.auth-container h1 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 2rem;
}

.auth-container p {
    color: #666;
    margin-bottom: 1.5rem;
}

/* Tabs de Autenticação */
.auth-tabs {
    display: flex;
    margin-bottom: 2rem;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 0.25rem;
}

.auth-tab-btn {
    flex: 1;
    padding: 0.75rem;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;
    color: #666;
}

.auth-tab-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* Formulários de Autenticação */
.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.input-group {
    margin-bottom: 1.5rem;
    text-align: left;
}

.input-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.input-group input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* Mensagens de Erro */
.input-error {
    display: block;
    color: #f87171;
    font-size: 0.8rem;
    margin-top: 0.25rem;
    min-height: 1rem;
}

/* Footer de Autenticação */
.auth-footer {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e1e5e9;
}

.auth-footer p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.auth-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Loading */
.auth-loading {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.auth-loading.active {
    display: flex;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.auth-loading p {
    color: #666;
    margin: 0;
}

/* Seletor de Avatar */
.avatar-selector {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.avatar-btn {
    background: #f8f9fa;
    border: 2px solid #e1e5e9;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s;
}

.avatar-btn:hover {
    transform: scale(1.1);
    border-color: #667eea;
}

.avatar-btn.active {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

/* Botões */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s;
    width: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
}

.btn-small {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    cursor: pointer;
}

.btn-send {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 10px;
    cursor: pointer;
    font-size: 1rem;
}

/* Chat Container */
#chat-screen.active {
    display: block;
}

.chat-container {
    display: grid;
    grid-template-areas:
        "header header"
        "sidebar main";
    grid-template-rows: 60px 1fr;
    grid-template-columns: 300px 1fr;
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
    background: white;
}

/* Header */
.chat-header {
    grid-area: header;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1rem;
    background: #667eea;
    color: white;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    font-size: 1.5rem;
    background: rgba(255,255,255,0.2);
    padding: 0.5rem;
    border-radius: 50%;
}

.user-name {
    font-weight: 600;
}

.user-id {
    font-size: 0.8rem;
    opacity: 0.8;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-logout {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-logout:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.status-indicator {
    font-size: 0.8rem;
}

.status-indicator.online {
    color: #4ade80;
}

.status-indicator.offline {
    color: #f87171;
}

/* Sidebar */
.sidebar {
    grid-area: sidebar;
    background: #f8f9fa;
    border-right: 1px solid #e1e5e9;
    display: flex;
    flex-direction: column;
}

.sidebar-tabs {
    display: flex;
    background: #e9ecef;
}

.tab-btn {
    flex: 1;
    padding: 0.75rem 0.5rem;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 0.8rem;
    transition: background-color 0.3s;
}

.tab-btn.active {
    background: #667eea;
    color: white;
}

.tab-content {
    display: none;
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
}

.tab-content.active {
    display: block;
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.tab-header h3 {
    color: #333;
    font-size: 1rem;
}

/* Listas */
.conversations-list,
.friend-requests-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.empty-state {
    text-align: center;
    color: #666;
    padding: 2rem 1rem;
}

.empty-state p {
    margin-bottom: 0.5rem;
}

/* Chat Main */
.chat-main {
    grid-area: main;
    display: flex;
    flex-direction: column;
    background: white;
}

.welcome-message {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: #666;
}

.welcome-message h2 {
    margin-bottom: 1rem;
    color: #333;
}

.chat-area {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-area.hidden {
    display: none;
}

.chat-header-conversation {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-bottom: 1px solid #e1e5e9;
    background: #f8f9fa;
}

.conversation-avatar {
    font-size: 1.5rem;
    background: #667eea;
    color: white;
    padding: 0.5rem;
    border-radius: 50%;
}

.conversation-name {
    font-weight: 600;
    color: #333;
}

.conversation-status {
    font-size: 0.8rem;
}

.conversation-status.online {
    color: #4ade80;
}

.conversation-status.offline {
    color: #9ca3af;
}

/* Mensagens */
.messages-container {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.message {
    display: flex;
    gap: 0.75rem;
    max-width: 70%;
}

.message.own {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-avatar {
    font-size: 1rem;
    background: #e9ecef;
    padding: 0.25rem;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.message.own .message-avatar {
    background: #667eea;
    color: white;
}

.message-content {
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 15px;
    border-bottom-left-radius: 5px;
}

.message.own .message-content {
    background: #667eea;
    color: white;
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 5px;
}

.message-text {
    margin-bottom: 0.25rem;
}

.message-time {
    font-size: 0.7rem;
    opacity: 0.7;
}

/* Input de Mensagem */
.message-input-container {
    padding: 1rem;
    border-top: 1px solid #e1e5e9;
    background: #f8f9fa;
}

#message-form {
    display: flex;
    gap: 0.5rem;
}

#message-input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #e1e5e9;
    border-radius: 25px;
    font-size: 1rem;
}

#message-input:focus {
    outline: none;
    border-color: #667eea;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e1e5e9;
}

.modal-header h3 {
    color: #333;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 1rem;
}

/* Resultados de Busca */
.search-results {
    margin-top: 1rem;
}

.user-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid #e1e5e9;
    border-radius: 10px;
    margin-bottom: 0.5rem;
}

.user-info-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar-item {
    font-size: 1.2rem;
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 50%;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name-item {
    font-weight: 600;
    color: #333;
}

.user-id-item {
    font-size: 0.8rem;
    color: #666;
}

.last-message {
    font-size: 0.75rem;
    color: #999;
    margin-top: 0.25rem;
    font-style: italic;
}

.conversation-item.active {
    background: #667eea;
    color: white;
}

.conversation-item.active .user-name-item,
.conversation-item.active .user-id-item,
.conversation-item.active .last-message {
    color: white;
}

.conversation-item {
    cursor: pointer;
    transition: all 0.3s;
}

.conversation-item:hover {
    background: #f0f0f0;
}

.status-icon {
    font-size: 0.7rem;
    margin-left: 0.5rem;
}

.status-icon.online {
    color: #4ade80;
}

.status-icon.offline {
    color: #9ca3af;
}

.btn-invite {
    background: #4ade80;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    cursor: pointer;
}

/* Estilos para dispositivos móveis */
.mobile-device .chat-container,
.tablet-device .chat-container {
    grid-template-areas:
        "header"
        "main";
    grid-template-rows: 60px 1fr;
    grid-template-columns: 1fr;
}

.mobile-device .sidebar,
.tablet-device .sidebar {
    position: fixed;
    top: 60px;
    left: -100%;
    width: 80%;
    height: calc(100vh - 60px);
    height: calc(var(--vh, 1vh) * 100 - 60px);
    z-index: 1000;
    transition: left 0.3s ease;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.mobile-device .sidebar.open,
.tablet-device .sidebar.open {
    left: 0;
}

.mobile-device .chat-main,
.tablet-device .chat-main {
    grid-area: main;
    height: calc(100vh - 60px);
    height: calc(var(--vh, 1vh) * 100 - 60px);
}

.mobile-device .message,
.tablet-device .message {
    max-width: 90%;
}

.mobile-device .messages-container,
.tablet-device .messages-container {
    padding: 0.5rem;
    padding-bottom: 80px; /* Espaço para o input fixo */
    height: calc(100% - 60px); /* Altura total menos header da conversa */
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.mobile-device .message-input-container,
.tablet-device .message-input-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #f8f9fa;
    border-top: 1px solid #e1e5e9;
    padding: 0.75rem;
    z-index: 100;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
}

.mobile-device #message-input,
.tablet-device #message-input {
    font-size: 16px; /* Previne zoom no iOS */
    padding: 0.75rem;
}

.mobile-device .btn-send,
.tablet-device .btn-send {
    padding: 0.75rem;
    min-width: 50px;
}

/* Botão de menu para mobile */
.mobile-menu-btn {
    display: none;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1.2rem;
}

.mobile-device .mobile-menu-btn,
.tablet-device .mobile-menu-btn {
    display: block;
}

/* Overlay para fechar sidebar */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 999;
}

.mobile-device .sidebar-overlay.active,
.tablet-device .sidebar-overlay.active {
    display: block;
}

/* Responsivo tradicional */
@media (max-width: 768px) {
    .desktop-device .chat-container {
        grid-template-areas:
            "header"
            "sidebar"
            "main";
        grid-template-rows: 60px auto 1fr;
        grid-template-columns: 1fr;
    }

    .desktop-device .sidebar {
        height: 200px;
    }

    .desktop-device .message {
        max-width: 85%;
    }
}
